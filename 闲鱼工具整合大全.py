import sys
import os
import asyncio
import logging
import traceback
import json
import requests
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QTextEdit, QProgressBar, 
                            QLabel, QFileDialog, QMessageBox, QSplitter,
                            QListWidget, QTimeEdit, QFormLayout, QGroupBox,
                            QFrame, QListWidgetItem, QStyle, QInputDialog, 
                            QDialog, QSpinBox, QDialogButtonBox, QStatusBar,
                            QSizePolicy)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTime, QSettings, QSize, QPoint
from PyQt6.QtGui import QFont, QIcon, QColor, QPalette, QClipboard
from 功能模块.收集订单 import OrderCollector
from 功能模块.批量擦亮 import AutoPolisher
from 功能模块.游戏采集 import GameCollector
from 功能模块.游戏名称清洗 import GameNameCleaner
from 功能模块.闲鱼发布器 import 闲鱼发布器
from 功能模块.退款订单查询 import RefundOrderQuerier
from 功能模块.批量下架 import BatchDelister
from 功能模块.批量修改重发 import BatchModifyRepost
from 功能模块.商品采集 import 商品采集器
from 功能模块.商品对比检查 import 商品对比检查器
from datetime import datetime
import pandas as pd

# 设置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class QTextEditLogger(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

    def emit(self, record):
        msg = self.format(record)
        self.text_widget.append(msg)
        # 添加自动滚动到底部
        self.text_widget.verticalScrollBar().setValue(self.text_widget.verticalScrollBar().maximum())

class OrderCollectorThread(QThread):
    progress_updated = pyqtSignal(int, int, int)  # 当前页, 总页数, 本页订单数
    finished = pyqtSignal(list)  # 订单ID列表
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path):
        super().__init__()
        self.config_path = config_path
        
    def run(self):
        try:
            collector = OrderCollector(
                self.config_path,
                log_func=lambda msg: print(msg)  # 日志直接打印到控制台
            )
            
            # 使用多线程方法收集订单
            all_orders = collector.collect_all_orders_threaded(
                progress_callback=lambda page, total, count: self.progress_updated.emit(page, total, count)
            )
            
            self.finished.emit(all_orders)
        except Exception as e:
            self.error.emit(str(e))

class AutoPolisherThread(QThread):
    progress_updated = pyqtSignal(int, int, int)  # 进度值, 成功数, 失败数
    status_updated = pyqtSignal(str, bool, str)   # 商品ID, 是否成功, 错误信息
    finished = pyqtSignal(dict)  # 结果字典
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path, goods_ids):
        super().__init__()
        self.config_path = config_path
        self.goods_ids = goods_ids
        
    def run(self):
        try:
            auto_polisher = AutoPolisher(
                self.config_path, 
                goods_ids=self.goods_ids,
                log_func=lambda msg: print(msg),  # 消息会在控制台输出，不影响GUI
                status_callback=lambda goods_id, success, error: 
                    self.status_updated.emit(goods_id, success, error if error else "")
            )
            result = auto_polisher.run(
                progress_callback=lambda progress, success, failed: 
                    self.progress_updated.emit(progress, success, failed)
            )
            
            # 不再保存结果到文件
            # auto_polisher.save_result(result)
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class GameCollectorThread(QThread):
    progress_updated = pyqtSignal(int, int, int)  # 当前页, 总页数, 发现游戏数
    log_updated = pyqtSignal(str)   # 日志信息
    finished = pyqtSignal(dict)  # 完成信号，返回结果字典
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path, hours_limit=24):
        super().__init__()
        self.config_path = config_path
        try:
            self.hours_limit = int(hours_limit)
            print(f"GameCollectorThread 初始化，时间范围: {self.hours_limit}")
        except (ValueError, TypeError) as e:
            print(f"时间范围转换错误: {e}，使用默认值24")
            self.hours_limit = 24
        
    def run(self):
        try:
            collector = GameCollector(
                self.config_path,
                log_func=lambda msg: self.log_updated.emit(msg)
            )
            
            result = collector.run(
                hours_limit=self.hours_limit,
                progress_callback=lambda page, total, count: 
                    self.progress_updated.emit(page, total, count)
            )
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class GameNameCleanerThread(QThread):
    log_updated = pyqtSignal(str)  # 日志信息
    progress_updated = pyqtSignal(int, int, str)  # 当前进度, 总数, 状态信息
    finished = pyqtSignal(str)  # 完成信号，返回结果文件路径
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path, input_file):
        super().__init__()
        self.config_path = config_path
        self.input_file = input_file
        
    def run(self):
        try:
            cleaner = GameNameCleaner(
                self.config_path,
                log_func=lambda msg: self.log_updated.emit(msg)
            )
            
            result = cleaner.run(
                self.input_file,
                progress_callback=lambda current, total, status: 
                    self.progress_updated.emit(current, total, status)
            )
            
            if result:
                self.finished.emit(result)
            else:
                self.error.emit("游戏名称清洗失败")
        except Exception as e:
            self.error.emit(str(e))

class RefundOrderQueryThread(QThread):
    log_updated = pyqtSignal(str)  # 日志信息
    progress_updated = pyqtSignal(int, int, int)  # 当前页, 总页数, 本页订单数
    finished = pyqtSignal(list)  # 退款订单列表
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path):
        super().__init__()
        self.config_path = config_path
        
    def run(self):
        try:
            querier = RefundOrderQuerier(
                self.config_path,
                log_func=lambda msg: self.log_updated.emit(msg)
            )
            
            # 查询所有退款订单
            refund_orders = querier.query_all_refund_orders(
                progress_callback=lambda page, total, count: 
                    self.progress_updated.emit(page, total, count)
            )
            
            # 保存到文本文件
            querier.save_to_file()
            
            # 导出到Excel
            querier.export_to_excel()
            
            # 提取手机号码并保存到单独文件
            querier.extract_phones_to_file()
            
            self.finished.emit(refund_orders)
        except Exception as e:
            self.error.emit(str(e))

class XianYuPublisherThread(QThread):
    log_updated = pyqtSignal(str)  # 日志信息
    progress_updated = pyqtSignal(int, int, int, int)  # 当前索引, 总数量, 成功数, 失败数
    finished = pyqtSignal(dict)  # 完成信号，返回统计结果
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path, folder_paths, interval=1):
        super().__init__()
        self.config_path = config_path
        self.folder_paths = folder_paths
        self.interval = interval
        self.publisher = None
        
    def run(self):
        try:
            self.publisher = 闲鱼发布器(
                self.config_path,
                log_func=lambda msg: self.log_updated.emit(msg)
            )
            
            self.publisher.publish_goods_batch(
                self.folder_paths,
                interval=self.interval,
                progress_callback=lambda current, total, success, failed: 
                    self.progress_updated.emit(current, total, success, failed)
            )
            
            stats = self.publisher.get_stats()
            self.finished.emit(stats)
        except Exception as e:
            self.error.emit(str(e))
            
    def stop(self):
        """停止发布任务"""
        if self.publisher:
            self.publisher.stop_publishing()

class BatchDelisterThread(QThread):
    progress_updated = pyqtSignal(int, int, int, int)  # 当前索引, 总数量, 成功数, 失败数
    status_updated = pyqtSignal(str, bool, str)   # 商品ID, 是否成功, 错误信息
    finished = pyqtSignal(dict)  # 结果字典
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path, goods_ids):
        super().__init__()
        self.config_path = config_path
        self.goods_ids = goods_ids
        self.delister = None
        
    def run(self):
        try:
            self.delister = BatchDelister(
                self.config_path,
                log_func=lambda msg: print(msg)  # 消息会在控制台输出，不影响GUI
            )
            
            result = self.delister.batch_delist(
                self.goods_ids,
                progress_callback=lambda current, total, success, failed: 
                    self.progress_updated.emit(current, total, success, failed),
                status_callback=lambda goods_id, success, error: 
                    self.status_updated.emit(goods_id, success, error if error else "")
            )
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))
            
    def stop(self):
        """停止下架任务"""
        if self.delister:
            self.delister.stop()

class BatchModifyRepostThread(QThread):
    progress_updated = pyqtSignal(int, int, int, int)  # 当前索引, 总数量, 成功数, 失败数
    status_updated = pyqtSignal(str, bool, str)   # 商品ID, 是否成功, 错误信息
    finished = pyqtSignal(dict)  # 结果字典
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, config_path, goods_ids):
        super().__init__()
        self.config_path = config_path
        self.goods_ids = goods_ids
        self.modifier = None
        
    def run(self):
        try:
            self.modifier = BatchModifyRepost(
                self.config_path,
                log_func=lambda msg: print(msg)  # 消息会在控制台输出，不影响GUI
            )
            
            result = self.modifier.batch_modify_repost(
                self.goods_ids,
                progress_callback=lambda current, total, success, failed: 
                    self.progress_updated.emit(current, total, success, failed),
                status_callback=lambda goods_id, success, error: 
                    self.status_updated.emit(goods_id, success, error if error else "")
            )
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))
            
    def stop(self):
        """停止修改重发任务"""
        if self.modifier:
            self.modifier.stop()

class ProductCollectorThread(QThread):
    progress_updated = pyqtSignal(int, int, int)  # 当前页码, 总页数, 已采集商品数
    log_updated = pyqtSignal(str)  # 日志信息
    finished = pyqtSignal(str, int)  # 结果文件路径, 符合条件的商品数量
    error = pyqtSignal(str)  # 错误信息

    def __init__(self, want_threshold):
        super().__init__()
        self.want_threshold = want_threshold
        self.collector = None

    def run(self):
        try:
            self.collector = 商品采集器(
                log_func=lambda msg: self.log_updated.emit(msg)
            )

            result_file, matched_count = self.collector.开始采集(
                self.want_threshold,
                progress_callback=lambda current_page, total_pages, collected_count:
                    self.progress_updated.emit(current_page, total_pages, collected_count)
            )

            if result_file:
                self.finished.emit(result_file, matched_count)
            else:
                self.error.emit("采集失败，未获取到有效数据")
        except Exception as e:
            self.error.emit(str(e))

class ProductCompareThread(QThread):
    progress_updated = pyqtSignal(int, int)  # 当前页, 总页数
    log_updated = pyqtSignal(str)  # 日志信息
    finished = pyqtSignal(dict)  # 完成信号，返回结果字典
    error = pyqtSignal(str)  # 错误信息

    def __init__(self, headers=None):
        super().__init__()
        self.headers = headers or {}

    def run(self):
        try:
            checker = 商品对比检查器(
                log_func=lambda msg: self.log_updated.emit(msg)
            )

            # 设置请求头
            if self.headers:
                checker.设置请求头(self.headers)

            result = checker.执行检查(
                progress_callback=lambda current, total:
                    self.progress_updated.emit(current, total)
            )

            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class TimeRangeDialog(QDialog):
    """自定义时间范围输入对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("时间范围设置")
        self.setMinimumWidth(300)
        self.collect_all = False  # 添加标志，表示是否采集全部游戏
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 添加标签
        label = QLabel("请输入要查询的时间范围（小时数）：")
        layout.addWidget(label)
        
        # 创建SpinBox
        self.spinbox = QSpinBox()
        self.spinbox.setMinimum(1)
        self.spinbox.setMaximum(999)  # 增加最大值到999
        self.spinbox.setValue(24)
        self.spinbox.setSingleStep(1)
        # 设置允许的位数更多
        self.spinbox.setDisplayIntegerBase(10)  # 确保使用十进制
        self.spinbox.setFixedWidth(150)  # 设置足够宽以显示三位数
        self.spinbox.setButtonSymbols(QSpinBox.ButtonSymbols.UpDownArrows)  # 使用上下箭头按钮
        
        # 添加到水平布局以便居中显示
        h_layout = QHBoxLayout()
        h_layout.addStretch()
        h_layout.addWidget(self.spinbox)
        h_layout.addStretch()
        layout.addLayout(h_layout)
        
        # 添加采集全部游戏按钮
        self.collect_all_button = QPushButton("采集全部游戏")
        self.collect_all_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        self.collect_all_button.clicked.connect(self.on_collect_all_clicked)
        
        # 添加按钮到水平布局
        collect_all_layout = QHBoxLayout()
        collect_all_layout.addStretch()
        collect_all_layout.addWidget(self.collect_all_button)
        collect_all_layout.addStretch()
        layout.addLayout(collect_all_layout)
        
        # 添加间距
        layout.addSpacing(10)
        
        # 添加标准按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def on_collect_all_clicked(self):
        """点击采集全部游戏按钮的处理函数"""
        self.collect_all = True
        self.accept()
        
    def get_value(self):
        """返回SpinBox的值或特殊值表示采集全部游戏"""
        if self.collect_all:
            print("采集全部游戏模式")
            return -1  # 返回-1表示采集全部游戏
        else:
            value = self.spinbox.value()
            print(f"TimeRangeDialog获取值: {value}, 类型: {type(value)}")
            return value

class WantThresholdDialog(QDialog):
    """自定义想要数阈值输入对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("想要数阈值设置")
        self.setMinimumWidth(300)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 添加标签
        label = QLabel("请输入想要数阈值（只采集想要数小于等于该值的商品）：")
        label.setWordWrap(True)
        layout.addWidget(label)
        
        # 创建SpinBox
        self.spinbox = QSpinBox()
        self.spinbox.setMinimum(0)
        self.spinbox.setMaximum(9999)  # 设置最大值
        self.spinbox.setValue(3)  # 默认值设为3
        self.spinbox.setSingleStep(1)
        self.spinbox.setDisplayIntegerBase(10)  # 确保使用十进制
        self.spinbox.setFixedWidth(150)  # 设置足够宽以显示数字
        self.spinbox.setButtonSymbols(QSpinBox.ButtonSymbols.UpDownArrows)  # 使用上下箭头按钮
        
        # 添加到水平布局以便居中显示
        h_layout = QHBoxLayout()
        h_layout.addStretch()
        h_layout.addWidget(self.spinbox)
        h_layout.addStretch()
        layout.addLayout(h_layout)
        
        # 添加说明文字
        info_label = QLabel("注意：数值越小，筛选出的商品越少，但质量可能更高")
        info_label.setStyleSheet("color: #666; font-style: italic;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)
        
        # 添加间距
        layout.addSpacing(10)
        
        # 添加标准按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_value(self):
        """返回SpinBox的值"""
        value = self.spinbox.value()
        return value

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("闲鱼工具整合大全")
        self.setMinimumSize(1200, 900)
        
        # 创建设置对象，用于保存窗口状态
        self.settings = QSettings("闲鱼工具", "闲鱼工具整合大全")
        
        # 确保图标文件夹存在
        icon_dir = "图标"
        if not os.path.exists(icon_dir):
            os.makedirs(icon_dir, exist_ok=True)
            logger.info(f"创建图标文件夹: {icon_dir}")
            
        # 设置窗口图标
        logo_path = os.path.join(icon_dir, "logo.png")
        if os.path.exists(logo_path):
            self.setWindowIcon(QIcon(logo_path))
        else:
            logger.info(f"图标文件未找到: {logo_path}")
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(12)  # 增加间距
        main_layout.setContentsMargins(15, 15, 15, 15)  # 调整边距
        
        # 创建水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setHandleWidth(2)  # 设置分割条宽度
        splitter.setChildrenCollapsible(False)  # 防止子部件被完全折叠
        main_layout.addWidget(splitter)
        
        # 左侧面板 - 订单ID列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 5, 0)  # 减小右边距，因为分割器已有间隔
        left_layout.setSpacing(8)  # 调整间距
        
        # 左侧标题
        left_title = QLabel("订单ID列表")
        left_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont("Microsoft YaHei")
        title_font.setPointSize(12)
        title_font.setBold(True)
        left_title.setFont(title_font)
        left_layout.addWidget(left_title)
        
        # 订单ID列表
        self.order_list = QListWidget()
        self.order_list.setAlternatingRowColors(True)
        left_layout.addWidget(self.order_list)
        
        # 左侧按钮布局
        left_buttons_layout = QHBoxLayout()
        
        # 刷新订单列表按钮
        refresh_btn = QPushButton("刷新订单列表")
        refresh_btn.clicked.connect(self.refresh_order_list)
        left_buttons_layout.addWidget(refresh_btn)
        
        # 复制全部ID按钮
        copy_all_btn = QPushButton("复制全部ID")
        copy_all_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        copy_all_btn.clicked.connect(self.copy_all_ids)
        left_buttons_layout.addWidget(copy_all_btn)
        
        left_layout.addLayout(left_buttons_layout)
        
        # 右侧面板 - 主要功能区
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 0, 0, 0)  # 减小左边距，因为分割器已有间隔
        right_layout.setSpacing(8)  # 调整间距
        
        # 创建标题标签
        title_label = QLabel("闲鱼工具整合大全")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_title_font = QFont("Microsoft YaHei")
        main_title_font.setPointSize(16)
        main_title_font.setBold(True)
        title_label.setFont(main_title_font)
        right_layout.addWidget(title_label)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(25)  # 设置固定高度
        self.progress_bar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)  # 水平扩展，垂直固定
        self.progress_bar.setValue(0)  # 初始值为0
        
        # 设置初始样式为隐藏状态
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid transparent;
                background-color: transparent;
                text-align: center;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: transparent;
            }
        """)
        
        # 创建进度条容器，保持固定高度
        progress_container = QWidget()
        progress_container.setFixedHeight(25)  # 与进度条相同的高度
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
        progress_layout.setSpacing(0)  # 移除间距
        progress_layout.addWidget(self.progress_bar)
        
        right_layout.addWidget(progress_container)
        
        # 添加进度条与下方内容之间的间距
        spacer = QWidget()
        spacer.setFixedHeight(5)  # 5像素的间距
        right_layout.addWidget(spacer)
        
        # 创建垂直分割器，分割日志区域和功能区域
        v_splitter = QSplitter(Qt.Orientation.Vertical)
        v_splitter.setHandleWidth(2)  # 设置分割条宽度
        v_splitter.setChildrenCollapsible(False)  # 防止子部件被完全折叠
        right_layout.addWidget(v_splitter)
        
        # 创建日志区域容器
        log_container = QWidget()
        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建日志标题和工具栏区域
        log_header = QHBoxLayout()
        
        # 创建日志标题
        log_title = QLabel("操作日志")
        log_title.setAlignment(Qt.AlignmentFlag.AlignLeft)
        log_font = QFont("Microsoft YaHei")
        log_font.setBold(True)
        log_title.setFont(log_font)
        log_header.addWidget(log_title)
        
        # 添加弹性空间
        log_header.addStretch()
        
        # 添加清除日志按钮
        clear_log_btn = QPushButton("清除日志")
        clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        clear_log_btn.clicked.connect(self.clear_log)
        log_header.addWidget(clear_log_btn)
        
        log_layout.addLayout(log_header)
        
        # 创建日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 设置文本自动换行
        from PyQt6.QtGui import QTextOption
        self.log_text.setWordWrapMode(QTextOption.WrapMode.WrapAtWordBoundaryOrAnywhere)
        
        # 设置滚动策略
        self.log_text.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.log_text.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 设置大小策略
        self.log_text.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # 设置日志区域样式
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 8px;
                font-family: 'Microsoft YaHei Mono', Consolas, monospace;
                font-size: 10pt;
                line-height: 1.3;
                color: #333;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #cdcdcd;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical:hover {
                background: #b0b0b0;
            }
        """)
        
        log_layout.addWidget(self.log_text)
        
        # 设置日志处理器
        log_handler = QTextEditLogger(self.log_text)
        log_handler.setLevel(logging.INFO)
        logger.addHandler(log_handler)
        
        # 创建功能区容器
        function_container = QWidget()
        function_container_layout = QVBoxLayout(function_container)
        function_container_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加认证信息管理区域
        auth_group = QGroupBox("认证信息管理")
        auth_layout = QVBoxLayout(auth_group)
        auth_layout.setSpacing(12)  # 增加间距
        auth_layout.setContentsMargins(15, 20, 15, 15)  # 调整内边距

        # 添加表单布局用于显示和编辑认证信息
        form_layout = QFormLayout()
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        form_layout.setHorizontalSpacing(10)  # 标签和字段之间的水平间距
        form_layout.setVerticalSpacing(8)    # 表单项之间的垂直间距

        # 创建输入框用于Authorization和Cookie
        self.auth_input = QTextEdit()
        self.auth_input.setFixedHeight(40)
        self.auth_input.setPlaceholderText("在这里输入Authorization值...")
        self.auth_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 3px;
                background-color: #fcfcfc;
            }
            QTextEdit:focus {
                border-color: #66afe9;
                outline: 0;
                box-shadow: 0 0 8px rgba(102, 175, 233, .6);
            }
        """)

        self.cookie_input = QTextEdit()
        self.cookie_input.setFixedHeight(60)  # 减小高度
        self.cookie_input.setPlaceholderText("在这里输入Cookie值...")
        self.cookie_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 3px;
                background-color: #fcfcfc;
            }
            QTextEdit:focus {
                border-color: #66afe9;
                outline: 0;
                box-shadow: 0 0 8px rgba(102, 175, 233, .6);
            }
        """)

        # 创建标签
        auth_label = QLabel("Authorization:")
        auth_label.setStyleSheet("font-weight: bold;")
        cookie_label = QLabel("Cookie:")
        cookie_label.setStyleSheet("font-weight: bold;")

        # 添加输入框到表单
        form_layout.addRow(auth_label, self.auth_input)
        form_layout.addRow(cookie_label, self.cookie_input)

        auth_layout.addLayout(form_layout)

        # 创建按钮布局
        auth_buttons_layout = QHBoxLayout()
        auth_buttons_layout.setSpacing(10)  # 按钮之间的间距

        # 设计认证按钮样式
        auth_button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b3d;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """

        # 加载认证信息按钮
        self.load_auth_button = QPushButton("加载认证信息")
        self.load_auth_button.setStyleSheet(auth_button_style)
        self.load_auth_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogOpenButton))
        self.load_auth_button.clicked.connect(self.load_auth_info_with_message)
        auth_buttons_layout.addWidget(self.load_auth_button)

        # 保存认证信息按钮
        self.save_auth_button = QPushButton("保存认证信息")
        self.save_auth_button.setStyleSheet(auth_button_style)
        self.save_auth_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        self.save_auth_button.clicked.connect(self.save_auth_info)
        auth_buttons_layout.addWidget(self.save_auth_button)

        # 验证认证信息按钮
        self.verify_auth_button = QPushButton("验证认证信息")
        self.verify_auth_button.setStyleSheet(auth_button_style)
        self.verify_auth_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogApplyButton))
        self.verify_auth_button.clicked.connect(self.verify_auth_info)
        auth_buttons_layout.addWidget(self.verify_auth_button)

        auth_layout.addLayout(auth_buttons_layout)
        
        # 添加认证信息区域到功能区容器
        function_container_layout.addWidget(auth_group)
        
        # 创建功能区框架
        function_group = QGroupBox("功能区")
        function_layout = QVBoxLayout(function_group)
        function_layout.setSpacing(15)  # 增加间距
        function_layout.setContentsMargins(15, 20, 15, 15)  # 增加内边距

        # 按钮区域 - 使用网格布局
        from PyQt6.QtWidgets import QGridLayout
        button_grid = QGridLayout()
        button_grid.setSpacing(10)  # 将按钮间距从10增加到15
        button_grid.setVerticalSpacing(15)  # 专门设置垂直间距为15像素
        button_grid.setHorizontalSpacing(10)  # 保持水平间距为10像素
        button_grid.setContentsMargins(5, 10, 5, 10)  # 增加上下边距

        # 设计按钮样式
        button_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 15px;  /* 增加内边距 */
                font-weight: bold;
                margin-top: 2px;     /* 增加上边距 */
                margin-bottom: 2px;  /* 增加下边距 */
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:pressed {
                background-color: #0a6fc2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """

        # 创建收集订单按钮
        self.collect_button = QPushButton("收集订单")
        self.collect_button.setMinimumHeight(45)  # 增加按钮高度
        self.collect_button.setStyleSheet(button_style)
        self.collect_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogListView))
        self.collect_button.clicked.connect(self.start_collect)
        button_grid.addWidget(self.collect_button, 0, 0)

        # 创建导出订单按钮
        self.export_button = QPushButton("导出订单")
        self.export_button.setMinimumHeight(45)  # 增加按钮高度
        self.export_button.setStyleSheet(button_style)
        self.export_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        self.export_button.clicked.connect(self.export_orders)
        button_grid.addWidget(self.export_button, 0, 1)

        # 创建批量擦亮按钮
        self.polish_button = QPushButton("批量擦亮")
        self.polish_button.setMinimumHeight(45)  # 增加按钮高度
        self.polish_button.setStyleSheet(button_style)
        self.polish_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        self.polish_button.clicked.connect(self.start_polish)
        button_grid.addWidget(self.polish_button, 1, 0)

        # 创建游戏采集按钮
        self.collect_game_button = QPushButton("游戏采集")
        self.collect_game_button.setMinimumHeight(45)  # 增加按钮高度
        self.collect_game_button.setStyleSheet(button_style)
        self.collect_game_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView))
        self.collect_game_button.clicked.connect(self.start_game_collect)
        button_grid.addWidget(self.collect_game_button, 1, 1)

        # 创建指定擦亮按钮
        self.specified_polish_button = QPushButton("指定擦亮")
        self.specified_polish_button.setMinimumHeight(45)  # 增加按钮高度
        self.specified_polish_button.setStyleSheet(button_style)
        self.specified_polish_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogStart))
        self.specified_polish_button.clicked.connect(self.start_specified_polish)
        button_grid.addWidget(self.specified_polish_button, 2, 0)

        # 创建游戏名称清洗按钮
        self.clean_game_name_button = QPushButton("游戏名称清洗")
        self.clean_game_name_button.setMinimumHeight(45)  # 增加按钮高度
        self.clean_game_name_button.setStyleSheet(button_style)
        self.clean_game_name_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogDetailedView))
        self.clean_game_name_button.clicked.connect(self.start_clean_game_name)
        button_grid.addWidget(self.clean_game_name_button, 2, 1)

        # 创建闲鱼发布器按钮
        self.publish_button = QPushButton("闲鱼发布器")
        self.publish_button.setMinimumHeight(45)  # 增加按钮高度
        self.publish_button.setStyleSheet(button_style)
        self.publish_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowUp))
        self.publish_button.clicked.connect(self.start_publish)
        button_grid.addWidget(self.publish_button, 3, 0)

        # 创建收集新增订单ID按钮
        self.collect_new_button = QPushButton("收集新增订单ID")
        self.collect_new_button.setMinimumHeight(45)  # 增加按钮高度
        self.collect_new_button.setStyleSheet(button_style)
        self.collect_new_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogNewFolder))
        self.collect_new_button.clicked.connect(self.start_collect_new_orders)
        button_grid.addWidget(self.collect_new_button, 3, 1)

        # 创建查询退款订单按钮
        self.query_refund_button = QPushButton("查询退款订单")
        self.query_refund_button.setMinimumHeight(45)  # 增加按钮高度
        self.query_refund_button.setStyleSheet(button_style)
        self.query_refund_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogDetailedView))
        self.query_refund_button.clicked.connect(self.start_query_refund)
        button_grid.addWidget(self.query_refund_button, 4, 0)

        # 创建批量下架按钮
        self.batch_delist_button = QPushButton("批量下架商品")
        self.batch_delist_button.setMinimumHeight(45)  # 增加按钮高度
        self.batch_delist_button.setStyleSheet(button_style)
        self.batch_delist_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowDown))
        self.batch_delist_button.clicked.connect(self.start_batch_delist)
        button_grid.addWidget(self.batch_delist_button, 4, 1)

        # 创建批量修改重发按钮
        self.batch_modify_repost_button = QPushButton("批量修改重发")
        self.batch_modify_repost_button.setMinimumHeight(45)  # 增加按钮高度
        self.batch_modify_repost_button.setStyleSheet(button_style)
        self.batch_modify_repost_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        self.batch_modify_repost_button.clicked.connect(self.start_batch_modify_repost)
        button_grid.addWidget(self.batch_modify_repost_button, 5, 0)

        # 创建采集商品ID按钮
        self.product_collector_button = QPushButton("采集商品ID")
        self.product_collector_button.setMinimumHeight(45)  # 增加按钮高度
        self.product_collector_button.setStyleSheet(button_style)
        self.product_collector_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView))
        self.product_collector_button.clicked.connect(self.start_product_collect)
        button_grid.addWidget(self.product_collector_button, 5, 1)

        # 创建商品对比检查按钮
        self.product_compare_button = QPushButton("商品对比检查")
        self.product_compare_button.setMinimumHeight(45)  # 增加按钮高度
        self.product_compare_button.setStyleSheet(button_style)
        self.product_compare_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogDetailedView))
        self.product_compare_button.clicked.connect(self.start_product_compare)
        button_grid.addWidget(self.product_compare_button, 6, 0)

        function_layout.addLayout(button_grid)
        
        # 添加功能区到功能区容器
        function_container_layout.addWidget(function_group)
        
        # 将日志区域和功能区域添加到垂直分割器
        v_splitter.addWidget(log_container)
        v_splitter.addWidget(function_container)
        
        # 设置垂直分割器的初始大小比例
        v_splitter.setSizes([350, 350])  # 日志区域和功能区域的初始高度比例
        
        # 添加面板到水平分割器
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setStretchFactor(0, 1)  # 左侧面板拉伸因子
        splitter.setStretchFactor(1, 4)  # 右侧面板拉伸因子，增加比例
        
        # 设置水平分割器的初始大小比例
        splitter.setSizes([250, 750])  # 左侧面板和右侧面板的初始宽度比例
        
        # 创建状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("就绪")
        
        # 初始化订单收集器
        self.order_collector = None
        self.collect_thread = None
        self.polish_thread = None
        self.publish_thread = None
        self.game_collect_thread = None
        self.game_name_cleaner_thread = None
        self.refund_thread = None
        self.batch_delist_thread = None
        self.batch_modify_repost_thread = None
        self.product_collector_thread = None
        self.product_compare_thread = None
        
        # 加载现有订单ID文件
        self.refresh_order_list()
        
        # 自动加载认证信息(不验证，不显示弹窗)
        try:
            self.load_auth_info(verify=False, show_message=False)
        except Exception as e:
            self.log_text.append(f"自动加载认证信息失败: {str(e)}")
            logger.error(f"自动加载认证信息失败: {str(e)}")
        
        # 将窗口移动到屏幕中央
        self.center_window()
        
        # 恢复之前保存的窗口大小和位置
        self.restore_window_settings()
        
        # 设置窗口关闭事件，保存窗口状态
        self.closeEvent = self.handle_close_event
        
        logger.info("主窗口初始化完成")
    
    def restore_window_settings(self):
        """恢复之前保存的窗口设置"""
        try:
            # 恢复窗口大小和位置
            size = self.settings.value("window_size")
            pos = self.settings.value("window_position")
            splitter_h_state = self.settings.value("splitter_h_state")
            splitter_v_state = self.settings.value("splitter_v_state")
            
            if size:
                self.resize(size)
            if pos:
                self.move(pos)
                
            # 如果窗口位置不在当前屏幕范围内，则重置到屏幕中央
            screen_rect = QApplication.primaryScreen().availableGeometry()
            window_rect = self.frameGeometry()
            if not screen_rect.contains(window_rect):
                self.center_window()
                
            # 恢复分割器状态
            if hasattr(self, 'splitter') and splitter_h_state:
                self.splitter.restoreState(splitter_h_state)
            if hasattr(self, 'v_splitter') and splitter_v_state:
                self.v_splitter.restoreState(splitter_v_state)
                
            logger.info("已恢复窗口设置")
        except Exception as e:
            logger.error(f"恢复窗口设置时出错: {str(e)}")
    
    def handle_close_event(self, event):
        """窗口关闭事件，保存窗口状态"""
        try:
            # 保存窗口大小和位置
            self.settings.setValue("window_size", self.size())
            self.settings.setValue("window_position", self.pos())
            
            # 保存分割器状态
            if hasattr(self, 'splitter'):
                self.settings.setValue("splitter_h_state", self.splitter.saveState())
            if hasattr(self, 'v_splitter'):
                self.settings.setValue("splitter_v_state", self.v_splitter.saveState())
                
            logger.info("已保存窗口设置")
        except Exception as e:
            logger.error(f"保存窗口设置时出错: {str(e)}")
        
        # 继续关闭窗口
        event.accept()
    
    def center_window(self):
        """将窗口移动到屏幕中央"""
        screen = QApplication.primaryScreen().geometry()
        window_size = self.geometry()
        x = (screen.width() - window_size.width()) // 2
        y = (screen.height() - window_size.height()) // 2
        self.move(x, y)
        logger.info(f"窗口已移动到屏幕中央，位置：({x}, {y})")
        
    def refresh_order_list(self):
        """刷新订单ID列表"""
        self.order_list.clear()
        order_id_file = os.path.join("配置文件", "订单ID.txt")
        
        if os.path.exists(order_id_file):
            try:
                with open(order_id_file, 'r', encoding='utf-8') as f:
                    order_ids = [line.strip() for line in f if line.strip()]
                    
                for order_id in order_ids:
                    item = QListWidgetItem(order_id)
                    self.order_list.addItem(item)
                    
                self.log_text.append(f"已加载 {len(order_ids)} 个订单ID")
            except Exception as e:
                self.log_text.append(f"加载订单ID文件失败: {str(e)}")
        else:
            self.log_text.append("未找到订单ID文件")

    def start_collect(self):
        self.collect_button.setEnabled(False)
        self.export_button.setEnabled(False)
        self.show_progress_bar()
        self.log_text.clear()
        self.statusBar.showMessage("正在收集订单...")
        
        config_path = os.path.join("配置文件", "商品配置.json")
        self.collect_thread = OrderCollectorThread(config_path)
        self.collect_thread.progress_updated.connect(self.update_progress)
        self.collect_thread.finished.connect(self.collection_finished)
        self.collect_thread.error.connect(self.collection_error)
        self.collect_thread.start()
        
    def update_progress(self, current_page, total_pages, orders_count):
        progress = int((current_page / total_pages) * 100)
        self.progress_bar.setValue(progress)
        self.log_text.append(f"正在处理第 {current_page}/{total_pages} 页，本页获取到 {orders_count} 个订单")
        
    def collection_finished(self, order_ids):
        self.order_collector = order_ids
        self.progress_bar.setValue(100)
        self.log_text.append(f"收集完成，共获取到 {len(order_ids)} 个订单")
        
        # 更新订单列表UI
        self.order_list.clear()
        for order_id in order_ids:
            item = QListWidgetItem(order_id)
            self.order_list.addItem(item)
        
        # 自动保存订单ID到文件
        try:
            # 保存到配置文件夹
            config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "配置文件")
            file_path = os.path.join(config_dir, "订单ID.txt")
            os.makedirs(config_dir, exist_ok=True)
            
            # 保存订单ID到文本文件
            with open(file_path, 'w', encoding='utf-8') as f:
                for order_id in order_ids:
                    f.write(f"{order_id}\n")
                    
            # 同时保存到输出文件夹
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件===")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, "订单ID.txt")
            
            # 复制文件
            import shutil
            shutil.copy2(file_path, output_path)
            
            self.log_text.append(f"订单ID已自动保存到：{file_path}")
            self.log_text.append(f"同时保存了一份到：{output_path}")
        except Exception as e:
            self.log_text.append(f"自动保存订单ID时出错：{str(e)}")
        
        self.collect_button.setEnabled(True)
        self.collect_new_button.setEnabled(True)  # 启用收集新增订单ID按钮
        self.export_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        
        self.statusBar.showMessage(f"收集完成，共获取到 {len(order_ids)} 个订单")
    
    def collection_error(self, error_message):
        QMessageBox.critical(self, "错误", f"收集过程中发生错误：{error_message}")
        self.log_text.append("发生错误")
        self.collect_button.setEnabled(True)
        self.collect_new_button.setEnabled(True)  # 启用收集新增订单ID按钮
        self.export_button.setEnabled(False)
        self.polish_button.setEnabled(False)
        self.specified_polish_button.setEnabled(False)
        self.collect_game_button.setEnabled(False)
        self.clean_game_name_button.setEnabled(False)
        
        self.statusBar.showMessage("收集订单失败", 5000)
    
    def start_polish(self):
        """开始批量擦亮功能"""
        # 从左侧列表获取订单ID
        order_ids = []
        for i in range(self.order_list.count()):
            item = self.order_list.item(i)
            if item.flags() & Qt.ItemFlag.ItemIsEnabled:  # 只选择启用的项
                order_ids.append(item.text())
        
        if not order_ids:
            QMessageBox.warning(self, "警告", "订单列表为空，请先收集订单")
            return
            
        self.collect_button.setEnabled(False)
        self.polish_button.setEnabled(False)
        self.specified_polish_button.setEnabled(False)
        self.collect_game_button.setEnabled(False)
        self.clean_game_name_button.setEnabled(False)
        self.export_button.setEnabled(False)
        self.show_progress_bar()
        self.log_text.clear()
        
        config_path = os.path.join("配置文件", "商品配置.json")
        self.log_text.append(f"开始批量擦亮商品，共 {len(order_ids)} 个，使用北京时间16:00...")
        
        # 设置所有商品的初始状态
        for i in range(self.order_list.count()):
            item = self.order_list.item(i)
            if item.text() in order_ids:
                item.setForeground(QColor("black"))  # 重置颜色
                item.setToolTip("等待处理...")
                
        self.polish_thread = AutoPolisherThread(config_path, order_ids)
        self.polish_thread.progress_updated.connect(self.update_polish_progress)
        self.polish_thread.status_updated.connect(self.update_item_status)
        self.polish_thread.finished.connect(self.polish_finished)
        self.polish_thread.error.connect(self.polish_error)
        self.polish_thread.start()
        
        self.statusBar.showMessage("正在批量擦亮商品...")
    
    def start_specified_polish(self):
        """开始指定擦亮功能"""
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择商品ID文件",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件==="),
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if not file_path:
            return  # 用户取消选择
            
        # 读取文件中的商品ID
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                order_ids = [line.strip() for line in f if line.strip()]
                
            if not order_ids:
                QMessageBox.warning(self, "警告", "选择的文件中没有找到有效的商品ID")
                return
                
            # 确认擦亮
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认擦亮")
            msg_box.setText(f"从文件中读取到 {len(order_ids)} 个商品ID")
            msg_box.setInformativeText("确认要开始擦亮这些商品吗？")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Yes)
            
            if msg_box.exec() != QMessageBox.StandardButton.Yes:
                return
                
            # 禁用相关按钮
            self.collect_button.setEnabled(False)
            self.polish_button.setEnabled(False)
            self.specified_polish_button.setEnabled(False)
            self.collect_game_button.setEnabled(False)
            self.clean_game_name_button.setEnabled(False)
            self.export_button.setEnabled(False)
            self.show_progress_bar()
            self.log_text.clear()
            
            config_path = os.path.join("配置文件", "商品配置.json")
            self.log_text.append(f"开始指定擦亮商品，共 {len(order_ids)} 个，使用北京时间16:00...")
            self.log_text.append(f"商品ID来源: {file_path}")
            
            # 清空左侧列表并添加要擦亮的商品ID
            self.order_list.clear()
            for order_id in order_ids:
                item = QListWidgetItem(order_id)
                item.setToolTip("等待处理...")
                self.order_list.addItem(item)
                
            # 创建并启动线程
            self.polish_thread = AutoPolisherThread(config_path, order_ids)
            self.polish_thread.progress_updated.connect(self.update_polish_progress)
            self.polish_thread.status_updated.connect(self.update_item_status)
            self.polish_thread.finished.connect(self.polish_finished)
            self.polish_thread.error.connect(self.polish_error)
            self.polish_thread.start()
            
            self.statusBar.showMessage("正在指定擦亮商品...")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取商品ID文件失败: {str(e)}")
            self.log_text.append(f"读取商品ID文件失败: {str(e)}")
            
    def update_item_status(self, goods_id, success, error_msg):
        """更新列表项状态"""
        # 查找对应的Item
        for i in range(self.order_list.count()):
            item = self.order_list.item(i)
            if item.text() == goods_id:
                if success:
                    item.setForeground(QColor("green"))
                    item.setToolTip("擦亮成功")
                else:
                    item.setForeground(QColor("red"))
                    item.setToolTip(f"擦亮失败: {error_msg}")
                break
        
        # 更新GUI
        QApplication.processEvents()

    def update_polish_progress(self, progress, success, failed):
        self.progress_bar.setValue(progress)
        self.log_text.append(f"批量擦亮进度: {progress}%, 成功: {success}, 失败: {failed}")
        
    def polish_finished(self, result):
        self.progress_bar.setValue(100)
        self.log_text.append(f"批量擦亮完成，共处理 {result['total']} 个商品，成功 {result['success']} 个，失败 {len(result['failed'])} 个")
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        self.statusBar.showMessage(f"批量擦亮完成，成功 {result['success']} 个，失败 {len(result['failed'])} 个")
    
    def polish_error(self, error_message):
        QMessageBox.critical(self, "错误", f"批量擦亮过程中发生错误：{error_message}")
        self.log_text.append(f"批量擦亮错误: {error_message}")
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        self.statusBar.showMessage("批量擦亮失败", 5000)
    
    def export_orders(self):
        if not self.order_collector:
            QMessageBox.warning(self, "警告", "请先收集订单")
            return
            
        try:
            # 直接保存到配置文件夹中的订单ID.txt
            config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "配置文件")
            file_path = os.path.join(config_dir, "订单ID.txt")
            
            # 确保目录存在
            os.makedirs(config_dir, exist_ok=True)
            
            # 保存订单ID到文本文件，每行一个ID
            with open(file_path, 'w', encoding='utf-8') as f:
                for order_id in self.order_collector:
                    f.write(f"{order_id}\n")
            
            # 同时保存一份到输出文件夹
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件===")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, "订单ID.txt")
            
            # 复制文件到输出目录
            import shutil
            shutil.copy2(file_path, output_path)
            
            self.log_text.append(f"订单已导出到：{file_path}")
            self.log_text.append(f"同时保存了一份到：{output_path}")
            QMessageBox.information(self, "成功", f"订单已保存到配置文件夹和输出文件夹中的订单ID.txt！\n共 {len(self.order_collector)} 个订单ID")
            
            # 刷新订单列表显示
            self.refresh_order_list()
            
            self.statusBar.showMessage(f"订单已导出，共 {len(self.order_collector)} 个订单ID")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出过程中发生错误：{str(e)}")
            self.log_text.append(f"导出错误：{str(e)}")
            
            self.statusBar.showMessage("导出订单失败", 5000)

    def copy_all_ids(self):
        """复制所有订单ID到剪贴板"""
        if self.order_list.count() == 0:
            QMessageBox.warning(self, "警告", "订单列表为空，没有可复制的ID")
            return
            
        all_ids = []
        for i in range(self.order_list.count()):
            all_ids.append(self.order_list.item(i).text())
        
        # 将ID列表复制到剪贴板，每行一个ID
        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(all_ids))
        
        self.log_text.append(f"已复制 {len(all_ids)} 个订单ID到剪贴板")
        QMessageBox.information(self, "成功", f"已复制 {len(all_ids)} 个订单ID到剪贴板")
        
        self.statusBar.showMessage(f"已复制 {len(all_ids)} 个订单ID到剪贴板", 3000)

    def start_game_collect(self):
        """开始游戏采集"""
        try:
            # 使用自定义对话框
            dialog = TimeRangeDialog(self)
            result = dialog.exec()
            
            if result != QDialog.DialogCode.Accepted:
                return
                
            # 获取时间范围
            hours_limit = dialog.get_value()
            print(f"时间范围输入: {hours_limit}, 类型: {type(hours_limit)}")
            
            # 判断是否是采集全部游戏模式
            is_collect_all = (hours_limit == -1)
            
            # 确保转换为整数
            if is_collect_all:
                hours_limit = 9999  # 使用一个非常大的值表示采集全部游戏
                self.log_text.append("已选择采集全部游戏模式，将不限制时间范围...")
            else:
                hours_limit = int(hours_limit)
            
            self.collect_button.setEnabled(False)
            self.polish_button.setEnabled(False)
            self.collect_game_button.setEnabled(False)
            self.clean_game_name_button.setEnabled(False)
            self.export_button.setEnabled(False)
            self.show_progress_bar()
            self.log_text.clear()
            
            config_path = os.path.join("配置文件", "商品配置.json")
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件===")
            
            if is_collect_all:
                self.log_text.append("开始采集全部游戏（不限时间范围）...")
            else:
                self.log_text.append(f"开始游戏采集（{hours_limit}小时内）...")
            
            # 创建GameCollector实例以测试连接
            collector = GameCollector(config_path, log_func=lambda msg: self.log_text.append(msg))
            connection_success, message = collector.test_connection()
            
            if not connection_success:
                self.log_text.append(f"\n❌ 连接测试失败: {message}")
                self.log_text.append("\n请检查网络连接后重试。")
                
                # 显示错误对话框
                QMessageBox.critical(self, "连接错误", 
                                    f"无法连接到游戏论坛网站。\n\n错误信息: {message}\n\n请检查网络连接后重试。")
                
                # 恢复按钮状态
                self.collect_button.setEnabled(True)
                self.polish_button.setEnabled(True)
                self.collect_game_button.setEnabled(True)
                self.clean_game_name_button.setEnabled(True)
                self.export_button.setEnabled(True)
                return
            
            self.game_collect_thread = GameCollectorThread(config_path, hours_limit)
            self.game_collect_thread.log_updated.connect(lambda msg: self.log_text.append(msg))
            self.game_collect_thread.progress_updated.connect(self.update_game_collect_progress)
            self.game_collect_thread.finished.connect(self.game_collect_finished)
            self.game_collect_thread.error.connect(self.game_collect_error)
            self.game_collect_thread.start()
            
            if is_collect_all:
                self.statusBar.showMessage("开始采集全部游戏（不限时间范围）...")
            else:
                self.statusBar.showMessage(f"开始游戏采集（{hours_limit}小时内）...")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"设置时间范围时发生错误: {str(e)}")
            self.log_text.append(f"游戏采集错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def update_game_collect_progress(self, page, total_pages, found_games):
        """更新游戏采集进度"""
        progress = int(page / max(total_pages, 1) * 100)
        self.progress_bar.setValue(progress)
        self.log_text.append(f"正在处理第 {page}/{max(total_pages, 1)} 页，已发现 {found_games} 个新游戏")
    
    def game_collect_finished(self, result):
        """游戏采集完成"""
        self.progress_bar.setValue(100)
        
        stats = result["statistics"]
        new_games = result["new_games"]
        duplicate_games = result["duplicate_games"]
        blacklisted_games = result["blacklisted_games"]
        whitelisted_games = result["whitelisted_games"]
        is_collect_all = result.get("is_collect_all", False)  # 判断是否是采集全部游戏模式
        
        # 检查是否有错误信息
        if "error" in result:
            self.log_text.append(f"\n❌ 游戏采集过程中发生错误: {result['error']}")
            if "traceback" in result:
                self.log_text.append("详细错误信息:")
                self.log_text.append(result["traceback"])
            
            # 显示错误对话框
            QMessageBox.warning(self, "游戏采集警告", 
                               f"游戏采集过程中发生错误，可能影响结果的完整性。\n\n错误信息: {result['error']}\n\n请检查网络连接或稍后重试。")
        
        # 显示结果摘要
        if is_collect_all:
            self.log_text.append(f"\n🌟 采集全部游戏模式完成")
            
        if new_games:
            self.log_text.append(f"\n✅ 发现 {len(new_games)} 个新游戏")
        else:
            self.log_text.append("\n❓ 未发现新的游戏")
            
        if duplicate_games:
            self.log_text.append(f"🔄 发现 {len(duplicate_games)} 个重复游戏")
            
        if whitelisted_games:
            self.log_text.append(f"✳️ 发现 {len(whitelisted_games)} 个白名单游戏")
            
        if blacklisted_games:
            self.log_text.append(f"⛔ 发现 {len(blacklisted_games)} 个黑名单游戏")
        
        # 显示文件保存信息
        if result["new_games_file"]:
            self.log_text.append(f"\n💾 新游戏ID已保存到: {os.path.basename(result['new_games_file'])}")
            
        if result["cleaned_names_file"]:
            self.log_text.append(f"💾 清洗后的游戏名称已保存到: {os.path.basename(result['cleaned_names_file'])}")
        
        # 显示统计信息
        self.log_text.append("\n📊 采集统计信息:")
        if is_collect_all:
            self.log_text.append(f"采集模式: 全部游戏（不限时间范围）")
        else:
            self.log_text.append(f"采集时间范围: {stats['hours_limit']}小时")
        self.log_text.append(f"查询页面数: {stats['total_pages']}页")
        self.log_text.append(f"已记录游戏总数: {stats['total_recorded_appids']}个")
        
        # 如果没有发现新游戏，但有查询到页面，可能是网站结构变化或所有游戏都已记录
        if not new_games and stats['total_pages'] > 0:
            self.log_text.append("\n⚠️ 提示: 未发现新游戏，可能原因:")
            self.log_text.append("1. 所有游戏已被记录")
            self.log_text.append("2. 网站结构可能已变化，无法正确解析")
            self.log_text.append("3. 网络连接不稳定，影响数据获取")
            self.log_text.append("\n建议检查网络连接或联系开发者更新程序。")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        # 根据模式显示不同的状态消息
        if is_collect_all:
            self.statusBar.showMessage(f"采集全部游戏完成，共查询{stats['total_pages']}页，发现 {len(new_games)} 个新游戏")
        else:
            self.statusBar.showMessage(f"游戏采集完成，发现 {len(new_games)} 个新游戏")
    
    def game_collect_error(self, error_message):
        """游戏采集错误处理"""
        self.progress_bar.setValue(0)  # 重置进度条
        
        # 显示详细错误信息
        self.log_text.append(f"\n❌ 游戏采集过程中发生严重错误：{error_message}")
        self.log_text.append("\n可能的原因:")
        self.log_text.append("1. 网络连接问题 - 无法访问游戏论坛网站")
        self.log_text.append("2. 网站结构变化 - 无法正确解析页面内容")
        self.log_text.append("3. 权限问题 - 无法写入输出文件")
        self.log_text.append("4. 程序错误 - 请联系开发者修复")
        
        self.log_text.append("\n建议解决方法:")
        self.log_text.append("1. 检查网络连接，确保可以访问游戏论坛网站")
        self.log_text.append("2. 确保程序有足够的权限写入文件")
        self.log_text.append("3. 重启程序后重试")
        self.log_text.append("4. 如果问题持续，请联系开发者更新程序")
        
        # 显示错误对话框
        QMessageBox.critical(self, "错误", 
                           f"游戏采集过程中发生严重错误：\n\n{error_message}\n\n请检查日志获取更多信息。")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        self.statusBar.showMessage("游戏采集失败", 5000)

    def start_clean_game_name(self):
        """开始游戏名称清洗"""
        # 选择输入文件
        input_file, _ = QFileDialog.getOpenFileName(
            self,
            "选择游戏名称文件",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件==="),
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if not input_file:
            return
            
        # 准备清洗
        self.collect_button.setEnabled(False)
        self.polish_button.setEnabled(False)
        self.collect_game_button.setEnabled(False)
        self.clean_game_name_button.setEnabled(False)
        self.export_button.setEnabled(False)
        self.show_progress_bar()
        self.log_text.clear()
        
        config_path = os.path.join("配置文件", "商品配置.json")
        self.log_text.append(f"开始游戏名称清洗，输入文件：{input_file}")
        
        self.game_name_cleaner_thread = GameNameCleanerThread(config_path, input_file)
        self.game_name_cleaner_thread.log_updated.connect(lambda msg: self.log_text.append(msg))
        self.game_name_cleaner_thread.progress_updated.connect(self.update_clean_game_name_progress)
        self.game_name_cleaner_thread.finished.connect(self.clean_game_name_finished)
        self.game_name_cleaner_thread.error.connect(self.clean_game_name_error)
        self.game_name_cleaner_thread.start()
        
        self.statusBar.showMessage("正在清洗游戏名称...")
    
    def update_clean_game_name_progress(self, current, total, status):
        """更新游戏名称清洗进度"""
        progress = int(current / max(total, 1) * 100)
        self.progress_bar.setValue(progress)
        
        if status:
            self.log_text.append(f"处理进度: {current}/{total} - {status}")
    
    def clean_game_name_finished(self, result_file):
        """游戏名称清洗完成"""
        self.progress_bar.setValue(100)
        self.log_text.append(f"游戏名称清洗完成！结果保存到: {result_file}")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        
        # 显示完成提示
        QMessageBox.information(self, "成功", f"游戏名称清洗完成!\n结果已保存到: {os.path.basename(result_file)}")
        
        self.statusBar.showMessage("游戏名称清洗完成")
    
    def clean_game_name_error(self, error_message):
        """游戏名称清洗错误处理"""
        self.hide_progress_bar()
        self.statusBar.showMessage("游戏名称清洗失败")
        QMessageBox.critical(self, "错误", f"游戏名称清洗失败: {error_message}")
        self.clean_game_name_button.setEnabled(True)
        logger.error(f"游戏名称清洗失败: {error_message}")
        
    def start_publish(self):
        """启动闲鱼发布器"""
        try:
            # 验证认证信息
            if not self.verify_auth_info():
                return
                
            # 选择包含游戏文件夹的目录
            dir_path = QFileDialog.getExistingDirectory(
                self, "选择包含游戏文件夹的目录", "", 
                QFileDialog.Option.ShowDirsOnly
            )
            
            if not dir_path:
                return
                
            # 获取目录下的所有文件夹
            folder_paths = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                if os.path.isdir(item_path):
                    folder_paths.append(item_path)
            
            if not folder_paths:
                QMessageBox.warning(self, "警告", "所选目录下没有找到文件夹")
                return
                
            # 获取发布间隔
            interval, ok = QInputDialog.getInt(
                self, "发布间隔", "请输入发布间隔（秒）:",
                1, 1, 300, 1
            )
            
            if not ok:
                return
                
            # 确认发布
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认发布")
            msg_box.setText(f"即将发布 {len(folder_paths)} 个商品，间隔 {interval} 秒")
            msg_box.setInformativeText("确认要开始发布吗？")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.No)
            
            if msg_box.exec() != QMessageBox.StandardButton.Yes:
                return
                
            # 禁用按钮
            self.publish_button.setEnabled(False)
            
            # 显示进度条
            self.show_progress_bar()
            self.statusBar.showMessage("正在发布商品...")
            
            # 创建并启动线程
            config_path = os.path.join("配置文件", "商品配置.json")
            
            # 先测试认证信息
            self.log_text.append("发布前测试认证信息...")
            test_publisher = 闲鱼发布器(
                config_path,
                log_func=lambda msg: self.log_text.append(msg)
            )
            
            # 记录认证测试信息到日志（不打印到控制台）
            self.log_text.append("开始认证测试...")
            self.log_text.append(f"配置文件路径: {config_path}")
            self.log_text.append(f"Authorization: {self.auth_input.toPlainText()[:20]}...")
            self.log_text.append(f"Cookie长度: {len(self.cookie_input.toPlainText())}")
            
            if not test_publisher.test_auth():
                QMessageBox.critical(self, "错误", "认证信息测试失败，请检查Authorization和Cookie是否有效")
                self.publish_button.setEnabled(True)
                self.hide_progress_bar()
                self.statusBar.showMessage("认证信息测试失败")
                return
                
            self.log_text.append("认证信息测试通过，开始发布商品...")
            
            # 创建并启动线程
            self.publish_thread = XianYuPublisherThread(config_path, folder_paths, interval)
            self.publish_thread.log_updated.connect(lambda msg: self.log_text.append(msg))
            self.publish_thread.progress_updated.connect(self.update_publish_progress)
            self.publish_thread.finished.connect(self.publish_finished)
            self.publish_thread.error.connect(self.publish_error)
            self.publish_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动发布器失败: {str(e)}")
            self.publish_button.setEnabled(True)
            self.hide_progress_bar()
            logger.error(f"启动发布器失败: {str(e)}")
    
    def update_publish_progress(self, current, total, success, failed):
        """更新发布进度"""
        progress = int((current / total) * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)
        self.statusBar.showMessage(f"正在发布: {current}/{total} | 成功: {success} | 失败: {failed}")
        
    def publish_finished(self, stats):
        """发布完成处理"""
        self.progress_bar.setValue(100)
        self.statusBar.showMessage("商品发布完成")
        self.publish_button.setEnabled(True)
        
        # 显示结果对话框
        msg = (
            f"发布完成！\n\n"
            f"总文件夹数: {stats['总文件夹数']}\n"
            f"成功发布数: {stats['成功发布数']}\n"
            f"失败发布数: {stats['失败发布数']}\n"
            f"总图片数: {stats['总图片数']}\n"
            f"成功上传图片数: {stats['成功上传图片数']}\n"
            f"失败上传图片数: {stats['失败上传图片数']}\n"
            f"运行时间: {stats['运行时间']}"
        )
        
        QMessageBox.information(self, "发布结果", msg)
        logger.info(f"商品发布完成: {msg}")
        
    def publish_error(self, error_message):
        """发布错误处理"""
        self.hide_progress_bar()
        self.statusBar.showMessage("商品发布失败")
        QMessageBox.critical(self, "错误", f"商品发布失败: {error_message}")
        self.publish_button.setEnabled(True)
        logger.error(f"商品发布失败: {error_message}")
        
    def load_auth_info(self, verify=True, show_message=False):
        """
        加载认证信息
        
        Args:
            verify: 是否验证认证信息
            show_message: 是否显示成功消息弹窗
        """
        try:
            config_path = os.path.join("配置文件", "商品配置.json")
            self.log_text.append("正在加载认证信息...")
            
            if not os.path.exists(config_path):
                if show_message:
                    QMessageBox.warning(self, "警告", f"配置文件不存在: {config_path}")
                self.log_text.append("配置文件不存在，无法加载认证信息")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            if "认证信息" in config and "Authorization" in config["认证信息"]:
                self.auth_input.setText(config["认证信息"]["Authorization"])
                self.log_text.append("成功加载 Authorization")
            else:
                self.log_text.append("未找到 Authorization 信息")
                
            if "认证信息" in config and "Cookie" in config["认证信息"]:
                self.cookie_input.setText(config["认证信息"]["Cookie"])
                self.log_text.append("成功加载 Cookie")
            else:
                self.log_text.append("未找到 Cookie 信息")
                
            # 如果需要验证认证信息，则调用verify_auth_info方法
            if verify:
                self.log_text.append("正在验证认证信息...")
                self.verify_auth_info()
            else:
                self.log_text.append("跳过认证信息验证")
                
            # 仅在需要时显示成功消息
            if show_message:
                QMessageBox.information(self, "成功", "认证信息加载成功")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载认证信息时发生错误: {str(e)}")
            self.log_text.append(f"加载认证信息错误: {str(e)}")
            
    def load_auth_info_with_message(self):
        """加载认证信息并显示消息弹窗"""
        self.load_auth_info(verify=True, show_message=True)
    
    def save_auth_info(self):
        """保存认证信息"""
        try:
            config_path = os.path.join("配置文件", "商品配置.json")
            self.log_text.append("正在保存认证信息...")
            
            # 读取现有配置
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
                
            # 确保认证信息字段存在
            if "认证信息" not in config:
                config["认证信息"] = {}
            
            # 更新认证信息
            auth_text = self.auth_input.toPlainText().strip()
            cookie_text = self.cookie_input.toPlainText().strip()
            
            if auth_text:
                config["认证信息"]["Authorization"] = auth_text
                self.log_text.append("已更新 Authorization")
            else:
                self.log_text.append("警告: Authorization 为空")
                
            if cookie_text:
                config["认证信息"]["Cookie"] = cookie_text
                self.log_text.append("已更新 Cookie")
            else:
                self.log_text.append("警告: Cookie 为空")
            
            # 保存配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
                
            QMessageBox.information(self, "成功", "认证信息保存成功")
            self.log_text.append("认证信息已保存到配置文件")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存认证信息时发生错误: {str(e)}")
            self.log_text.append(f"保存认证信息错误: {str(e)}")
    
    def verify_auth_info(self):
        """验证认证信息是否有效"""
        try:
            # 获取当前输入框中的认证信息
            auth_text = self.auth_input.toPlainText().strip()
            cookie_text = self.cookie_input.toPlainText().strip()
            
            if not auth_text or not cookie_text:
                QMessageBox.warning(self, "警告", "Authorization 或 Cookie 不能为空")
                self.log_text.append("认证信息不完整，无法验证")
                return False
            
            self.log_text.append("正在验证认证信息...")
            self.verify_auth_button.setEnabled(False)
            
            # 创建临时认证信息
            auth_info = {
                "Authorization": auth_text,
                "Cookie": cookie_text
            }
            
            # 执行验证
            # 自定义简单API测试
            def 本地API测试(认证信息, log_func=None):
                """测试API连接是否正常"""
                if log_func:
                    log_func("正在测试API连接...")
                
                try:
                    # 构建请求头
                    headers = {
                        "accept": "application/json, text/plain, */*",
                        "authorization": 认证信息["Authorization"],
                        "content-type": "application/json;charset=UTF-8",
                        "origin": "https://aldsidle.agiso.com",
                        "referer": "https://aldsidle.agiso.com/",
                        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    }
                    
                    # 解析Cookie
                    cookies = {}
                    cookie字符串 = 认证信息.get("Cookie", "")
                    for item in cookie字符串.split(';'):
                        if '=' in item:
                            name, value = item.strip().split('=', 1)
                            cookies[name] = value
                    
                    # 测试用户信息API
                    test_url = "https://aldsidle.agiso.com/api/User/GetUserInfo"
                    if log_func:
                        log_func(f"发送测试请求到: {test_url}")
                    
                    response = requests.get(
                        test_url,
                        headers=headers,
                        cookies=cookies,
                        timeout=10
                    )
                    
                    if log_func:
                        log_func(f"测试请求状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get('succeeded'):
                                if log_func:
                                    log_func("✅ API连接测试成功")
                                return True
                            else:
                                if log_func:
                                    log_func(f"❌ API连接测试失败: {result.get('message', '未知错误')}")
                                return False
                        except Exception:
                            if log_func:
                                log_func("❌ 无法解析API响应")
                            return False
                    else:
                        if log_func:
                            log_func(f"❌ API连接测试失败，状态码: {response.status_code}")
                        return False
                        
                except Exception as e:
                    if log_func:
                        log_func(f"❌ API连接测试失败，发生异常: {str(e)}")
                    return False
            
            result = 本地API测试(auth_info, lambda msg: self.log_text.append(msg))
            
            if result:
                QMessageBox.information(self, "成功", "认证信息验证通过！")
                self.log_text.append("✅ 认证信息验证成功")
                return True
            else:
                QMessageBox.warning(self, "警告", "认证信息验证失败，可能已过期！")
                self.log_text.append("❌ 认证信息验证失败")
                return False
        except Exception as e:
            QMessageBox.critical(self, "错误", f"验证认证信息时发生错误: {str(e)}")
            self.log_text.append(f"验证认证信息错误: {str(e)}")
            return False
        finally:
            self.verify_auth_button.setEnabled(True)

    def clear_log(self):
        """清除日志区域内容"""
        self.log_text.clear()
        self.statusBar.showMessage("日志已清除", 3000)  # 显示3秒

    def show_progress_bar(self):
        """显示进度条"""
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bbb;
                border-radius: 3px;
                background-color: #f0f0f0;
                text-align: center;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)
        
    def hide_progress_bar(self):
        """隐藏进度条"""
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid transparent;
                background-color: transparent;
                text-align: center;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: transparent;
            }
        """)

    def start_collect_new_orders(self):
        """收集新增订单ID功能"""
        self.collect_button.setEnabled(False)
        self.collect_new_button.setEnabled(False)
        self.export_button.setEnabled(False)
        self.show_progress_bar()
        self.log_text.clear()
        self.statusBar.showMessage("正在收集新增订单ID...")
        
        config_path = os.path.join("配置文件", "商品配置.json")
        self.collect_thread = OrderCollectorThread(config_path)
        self.collect_thread.progress_updated.connect(self.update_progress)
        # 使用不同的回调函数处理收集完成事件
        self.collect_thread.finished.connect(self.collection_new_finished)
        self.collect_thread.error.connect(self.collection_error)
        self.collect_thread.start()

    def collection_new_finished(self, order_ids):
        """处理新增订单ID收集完成事件"""
        self.order_collector = order_ids
        self.progress_bar.setValue(100)
        self.log_text.append(f"收集完成，共获取到 {len(order_ids)} 个订单")
        
        # 更新订单列表UI
        self.order_list.clear()
        for order_id in order_ids:
            item = QListWidgetItem(order_id)
            self.order_list.addItem(item)
        
        # 读取已有的订单ID文件
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件===")
        existing_orders_file = os.path.join(output_dir, "订单ID.txt")
        existing_orders = []
        
        try:
            if os.path.exists(existing_orders_file):
                with open(existing_orders_file, 'r', encoding='utf-8') as f:
                    existing_orders = [line.strip() for line in f if line.strip()]
                self.log_text.append(f"已读取现有订单ID文件，共 {len(existing_orders)} 个订单")
            else:
                self.log_text.append("未找到现有订单ID文件，将所有订单视为新增")
        except Exception as e:
            self.log_text.append(f"读取现有订单ID文件时出错：{str(e)}")
            existing_orders = []
        
        # 找出新增订单ID
        new_orders = [order_id for order_id in order_ids if order_id not in existing_orders]
        
        if not new_orders:
            self.log_text.append("未发现新增订单ID")
            QMessageBox.information(self, "结果", "未发现新增订单ID")
        else:
            self.log_text.append(f"发现 {len(new_orders)} 个新增订单ID")
            
            # 保存新增订单ID到文件
            try:
                # 生成带时间戳的文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_orders_file = os.path.join(output_dir, f"新增订单ID_{timestamp}.txt")
                
                with open(new_orders_file, 'w', encoding='utf-8') as f:
                    for order_id in new_orders:
                        f.write(f"{order_id}\n")
                
                self.log_text.append(f"新增订单ID已保存到：{new_orders_file}")
                QMessageBox.information(self, "成功", f"已发现并保存 {len(new_orders)} 个新增订单ID")
                
                # 高亮显示新增订单ID
                for i in range(self.order_list.count()):
                    item = self.order_list.item(i)
                    if item.text() in new_orders:
                        item.setForeground(QColor("blue"))
                        item.setToolTip("新增订单")
            except Exception as e:
                self.log_text.append(f"保存新增订单ID时出错：{str(e)}")
                QMessageBox.warning(self, "警告", f"保存新增订单ID时出错：{str(e)}")
        
        # 自动保存所有订单ID到标准文件
        try:
            # 保存到配置文件夹
            config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "配置文件")
            file_path = os.path.join(config_dir, "订单ID.txt")
            os.makedirs(config_dir, exist_ok=True)
            
            # 保存订单ID到文本文件
            with open(file_path, 'w', encoding='utf-8') as f:
                for order_id in order_ids:
                    f.write(f"{order_id}\n")
                    
            # 同时保存到输出文件夹
            output_path = os.path.join(output_dir, "订单ID.txt")
            
            # 复制文件
            import shutil
            shutil.copy2(file_path, output_path)
            
            self.log_text.append(f"所有订单ID已自动保存到：{file_path}")
            self.log_text.append(f"同时更新了标准文件：{output_path}")
        except Exception as e:
            self.log_text.append(f"自动保存订单ID时出错：{str(e)}")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.collect_new_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        
        self.statusBar.showMessage(f"收集完成，发现 {len(new_orders)} 个新增订单ID")

    def start_query_refund(self):
        """查询退款订单功能"""
        # 验证认证信息
        if not self.verify_auth_info():
            return
            
        self.query_refund_button.setEnabled(False)
        self.show_progress_bar()
        self.log_text.clear()
        self.statusBar.showMessage("正在查询退款订单...")
        
        config_path = os.path.join("配置文件", "商品配置.json")
        self.refund_thread = RefundOrderQueryThread(config_path)
        self.refund_thread.log_updated.connect(lambda msg: self.log_text.append(msg))
        self.refund_thread.progress_updated.connect(self.update_refund_progress)
        self.refund_thread.finished.connect(self.query_refund_finished)
        self.refund_thread.error.connect(self.query_refund_error)
        self.refund_thread.start()
    
    def update_refund_progress(self, current_page, total_pages, orders_count):
        """更新退款订单查询进度"""
        progress = int((current_page / total_pages) * 100) if total_pages > 0 else 0
        self.progress_bar.setValue(progress)
        self.log_text.append(f"正在处理第 {current_page}/{total_pages} 页，本页获取到 {orders_count} 个退款订单")
    
    def query_refund_finished(self, refund_orders):
        """退款订单查询完成处理"""
        self.progress_bar.setValue(100)
        
        if not refund_orders:
            self.log_text.append("未查询到退款订单")
            QMessageBox.information(self, "查询结果", "未查询到退款订单")
        else:
            self.log_text.append(f"查询完成，共获取到 {len(refund_orders)} 个退款订单")
            self.log_text.append("退款订单数据已保存到文本文件和Excel文件")
            self.log_text.append("手机号码已单独提取并保存到单独的文本文件中")
            QMessageBox.information(self, "查询结果", 
                                   f"查询完成，共获取到 {len(refund_orders)} 个退款订单\n"
                                   f"数据已保存到'===输出文件==='文件夹\n"
                                   f"手机号码已单独提取并保存为独立文本文件")
        
        self.query_refund_button.setEnabled(True)
        self.statusBar.showMessage(f"退款订单查询完成，共 {len(refund_orders)} 个订单")
    
    def query_refund_error(self, error_message):
        """退款订单查询错误处理"""
        self.hide_progress_bar()
        self.log_text.append(f"查询退款订单失败: {error_message}")
        QMessageBox.critical(self, "错误", f"查询退款订单失败: {error_message}")
        self.query_refund_button.setEnabled(True)
        self.statusBar.showMessage("退款订单查询失败", 5000)

    def start_batch_delist(self):
        """开始批量下架商品"""
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择商品ID文件",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件==="),
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if not file_path:
            return  # 用户取消选择
            
        # 读取文件中的商品ID
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                goods_ids = [line.strip() for line in f if line.strip()]
                
            if not goods_ids:
                QMessageBox.warning(self, "警告", "选择的文件中没有找到有效的商品ID")
                return
                
            # 确认下架
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认下架")
            msg_box.setText(f"从文件中读取到 {len(goods_ids)} 个商品ID")
            msg_box.setInformativeText("确认要开始下架这些商品吗？")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.No)
            
            if msg_box.exec() != QMessageBox.StandardButton.Yes:
                return
                
            # 验证认证信息
            if not self.verify_auth_info():
                return
                
            # 禁用相关按钮
            self.collect_button.setEnabled(False)
            self.polish_button.setEnabled(False)
            self.specified_polish_button.setEnabled(False)
            self.collect_game_button.setEnabled(False)
            self.clean_game_name_button.setEnabled(False)
            self.export_button.setEnabled(False)
            self.batch_delist_button.setEnabled(False)
            self.show_progress_bar()
            self.log_text.clear()
            
            config_path = os.path.join("配置文件", "商品配置.json")
            self.log_text.append(f"开始批量下架商品，共 {len(goods_ids)} 个")
            self.log_text.append(f"商品ID来源: {file_path}")
            
            # 清空左侧列表并添加要下架的商品ID
            self.order_list.clear()
            for goods_id in goods_ids:
                item = QListWidgetItem(goods_id)
                item.setToolTip("等待处理...")
                self.order_list.addItem(item)
                
            # 创建并启动线程
            self.batch_delist_thread = BatchDelisterThread(config_path, goods_ids)
            self.batch_delist_thread.progress_updated.connect(self.update_batch_delist_progress)
            self.batch_delist_thread.status_updated.connect(self.update_item_status)
            self.batch_delist_thread.finished.connect(self.batch_delist_finished)
            self.batch_delist_thread.error.connect(self.batch_delist_error)
            self.batch_delist_thread.start()
            
            self.statusBar.showMessage("正在批量下架商品...")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取商品ID文件失败: {str(e)}")
            self.log_text.append(f"读取商品ID文件失败: {str(e)}")
            
    def update_batch_delist_progress(self, current, total, success, failed):
        """更新批量下架进度"""
        progress = int((current / total) * 100)
        self.progress_bar.setValue(progress)
        self.log_text.append(f"批量下架进度: {progress}%, 成功: {success}, 失败: {failed}")
    
    def batch_delist_finished(self, result):
        """批量下架完成处理"""
        self.progress_bar.setValue(100)
        failed_count = result['failed']  # 直接使用返回结果中的failed_count
        failed_items = result['failed_items']  # 失败项目列表
        self.log_text.append(f"批量下架完成，共处理 {result['total']} 个商品，成功 {result['success']} 个，失败 {failed_count} 个")
        
        # 如果有失败项目，显示详情
        if failed_items:
            self.log_text.append("\n失败项目详情:")
            for item in failed_items:
                goods_id = item['goods_id']
                error = item['error']
                self.log_text.append(f"- 商品 {goods_id} 失败: {error}")
        
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        
        # 显示完成消息
        QMessageBox.information(self, "完成", f"批量下架完成，共处理 {result['total']} 个商品，成功 {result['success']} 个，失败 {failed_count} 个")
        
        self.statusBar.showMessage(f"批量下架完成，成功 {result['success']} 个，失败 {failed_count} 个")
    
    def batch_delist_error(self, error_message):
        """批量下架错误处理"""
        self.hide_progress_bar()
        self.log_text.append(f"批量下架错误: {error_message}")
        QMessageBox.critical(self, "错误", f"批量下架过程中发生错误：{error_message}")
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        
        self.statusBar.showMessage("批量下架失败", 5000)

    def start_batch_modify_repost(self):
        """开始批量修改重发"""
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择商品ID文件",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件==="),
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if not file_path:
            return  # 用户取消选择
            
        # 读取文件中的商品ID
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                goods_ids = [line.strip() for line in f if line.strip()]
                
            if not goods_ids:
                QMessageBox.warning(self, "警告", "选择的文件中没有找到有效的商品ID")
                return
                
            # 确认修改重发
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认修改重发")
            msg_box.setText(f"从文件中读取到 {len(goods_ids)} 个商品ID")
            msg_box.setInformativeText("确认要开始修改重发这些商品吗？这将调整商品图片顺序并重新发布。")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.No)
            
            if msg_box.exec() != QMessageBox.StandardButton.Yes:
                return
                
            # 验证认证信息
            if not self.verify_auth_info():
                return
                
            # 禁用相关按钮
            self.collect_button.setEnabled(False)
            self.polish_button.setEnabled(False)
            self.specified_polish_button.setEnabled(False)
            self.collect_game_button.setEnabled(False)
            self.clean_game_name_button.setEnabled(False)
            self.export_button.setEnabled(False)
            self.batch_delist_button.setEnabled(False)
            self.batch_modify_repost_button.setEnabled(False)
            self.show_progress_bar()
            self.log_text.clear()
            
            config_path = os.path.join("配置文件", "商品配置.json")
            self.log_text.append(f"开始批量修改重发商品，共 {len(goods_ids)} 个")
            self.log_text.append(f"商品ID来源: {file_path}")
            
            # 清空左侧列表并添加要修改重发的商品ID
            self.order_list.clear()
            for goods_id in goods_ids:
                item = QListWidgetItem(goods_id)
                item.setToolTip("等待处理...")
                self.order_list.addItem(item)
                
            # 创建并启动线程
            self.batch_modify_repost_thread = BatchModifyRepostThread(config_path, goods_ids)
            self.batch_modify_repost_thread.progress_updated.connect(self.update_batch_modify_repost_progress)
            self.batch_modify_repost_thread.status_updated.connect(self.update_item_status)
            self.batch_modify_repost_thread.finished.connect(self.batch_modify_repost_finished)
            self.batch_modify_repost_thread.error.connect(self.batch_modify_repost_error)
            self.batch_modify_repost_thread.start()
            
            self.statusBar.showMessage("正在批量修改重发商品...")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取商品ID文件失败: {str(e)}")
            self.log_text.append(f"读取商品ID文件失败: {str(e)}")
            
    def update_batch_modify_repost_progress(self, current, total, success, failed):
        """更新批量修改重发进度"""
        progress = int((current / total) * 100)
        self.progress_bar.setValue(progress)
        self.log_text.append(f"批量修改重发进度: {progress}%, 成功: {success}, 失败: {failed}")
    
    def batch_modify_repost_finished(self, result):
        """批量修改重发完成处理"""
        self.progress_bar.setValue(100)
        failed_count = len(result.get('failed_items', []))
        self.log_text.append(f"批量修改重发完成，共处理 {result['total']} 个商品，成功 {result['success']} 个，失败 {failed_count} 个")
        
        # 如果有失败项目，显示详情
        if 'failed_items' in result and result['failed_items']:
            self.log_text.append("\n失败项目详情:")
            for item in result['failed_items']:
                goods_id = item['goods_id']
                error = item['error']
                self.log_text.append(f"- 商品 {goods_id} 失败: {error}")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        self.batch_modify_repost_button.setEnabled(True)
        
        # 显示完成消息
        QMessageBox.information(self, "完成", f"批量修改重发完成，共处理 {result['total']} 个商品，成功 {result['success']} 个，失败 {failed_count} 个")
        
        self.statusBar.showMessage(f"批量修改重发完成，成功 {result['success']} 个，失败 {failed_count} 个")
    
    def batch_modify_repost_error(self, error_message):
        """批量修改重发错误处理"""
        self.hide_progress_bar()
        self.log_text.append(f"批量修改重发错误: {error_message}")
        QMessageBox.critical(self, "错误", f"批量修改重发过程中发生错误：{error_message}")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        self.batch_modify_repost_button.setEnabled(True)
        
        self.statusBar.showMessage("批量修改重发失败", 5000)

    def start_product_collect(self):
        """开始采集商品ID"""
        try:
            # 使用自定义对话框获取想要数阈值
            dialog = WantThresholdDialog(self)
            result = dialog.exec()
            
            if result != QDialog.DialogCode.Accepted:
                return
                
            # 获取想要数阈值
            want_threshold = dialog.get_value()
            self.log_text.append(f"设置想要数阈值为: {want_threshold}")
            
            # 禁用相关按钮
            self.collect_button.setEnabled(False)
            self.polish_button.setEnabled(False)
            self.specified_polish_button.setEnabled(False)
            self.collect_game_button.setEnabled(False)
            self.clean_game_name_button.setEnabled(False)
            self.export_button.setEnabled(False)
            self.batch_delist_button.setEnabled(False)
            self.batch_modify_repost_button.setEnabled(False)
            self.product_collector_button.setEnabled(False)
            self.show_progress_bar()
            self.log_text.clear()
            
            self.log_text.append(f"开始采集商品ID，想要数阈值: {want_threshold}")
            
            # 创建并启动线程
            self.product_collector_thread = ProductCollectorThread(want_threshold)
            self.product_collector_thread.log_updated.connect(lambda msg: self.log_text.append(msg))
            self.product_collector_thread.progress_updated.connect(self.update_product_collect_progress)
            self.product_collector_thread.finished.connect(self.product_collect_finished)
            self.product_collector_thread.error.connect(self.product_collect_error)
            self.product_collector_thread.start()
            
            self.statusBar.showMessage("正在采集商品ID...")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动商品采集器失败: {str(e)}")
            self.log_text.append(f"启动商品采集器失败: {str(e)}")
            
            # 恢复按钮状态
            self.collect_button.setEnabled(True)
            self.polish_button.setEnabled(True)
            self.specified_polish_button.setEnabled(True)
            self.collect_game_button.setEnabled(True)
            self.clean_game_name_button.setEnabled(True)
            self.export_button.setEnabled(True)
            self.batch_delist_button.setEnabled(True)
            self.batch_modify_repost_button.setEnabled(True)
            self.product_collector_button.setEnabled(True)
    
    def update_product_collect_progress(self, current_page, total_pages, collected_count):
        """更新商品ID采集进度"""
        progress = int((current_page / total_pages) * 100)
        self.progress_bar.setValue(progress)
        self.log_text.append(f"正在处理第 {current_page}/{total_pages} 页，已采集到 {collected_count} 个商品ID")
    
    def product_collect_finished(self, result_file, matched_count):
        """商品ID采集完成"""
        self.progress_bar.setValue(100)
        self.log_text.append(f"商品ID采集完成！结果保存到: {os.path.basename(result_file)}")
        self.log_text.append(f"共有 {matched_count} 个商品符合条件")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        self.batch_modify_repost_button.setEnabled(True)
        self.product_collector_button.setEnabled(True)
        
        # 显示完成提示
        QMessageBox.information(self, "成功", f"商品ID采集完成!\n共采集到 {matched_count} 个符合条件的商品ID\n结果已保存到: {os.path.basename(result_file)}")
        
        self.statusBar.showMessage("商品ID采集完成")
    
    def product_collect_error(self, error_message):
        """商品ID采集错误处理"""
        self.hide_progress_bar()
        self.statusBar.showMessage("商品ID采集失败")
        QMessageBox.critical(self, "错误", f"商品ID采集失败: {error_message}")
        
        # 恢复按钮状态
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        self.batch_modify_repost_button.setEnabled(True)
        self.product_collector_button.setEnabled(True)
        
        self.statusBar.showMessage("商品ID采集失败", 5000)

    def start_product_compare(self):
        """开始商品对比检查"""
        # 检查是否有认证信息
        auth_text = self.auth_input.toPlainText().strip()
        cookie_text = self.cookie_input.toPlainText().strip()

        if not auth_text or not cookie_text:
            QMessageBox.warning(self, "警告", "请先填写Authorization和Cookie信息")
            return

        # 构建请求头
        headers = {
            'Authorization': auth_text,
            'Cookie': cookie_text,
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        # 禁用相关按钮
        self.product_compare_button.setEnabled(False)
        self.collect_button.setEnabled(False)
        self.polish_button.setEnabled(False)
        self.specified_polish_button.setEnabled(False)
        self.collect_game_button.setEnabled(False)
        self.clean_game_name_button.setEnabled(False)
        self.export_button.setEnabled(False)
        self.batch_delist_button.setEnabled(False)
        self.batch_modify_repost_button.setEnabled(False)
        self.product_collector_button.setEnabled(False)

        self.show_progress_bar()
        self.log_text.clear()
        self.statusBar.showMessage("正在进行商品对比检查...")

        # 创建并启动线程
        self.product_compare_thread = ProductCompareThread(headers)
        self.product_compare_thread.progress_updated.connect(self.update_compare_progress)
        self.product_compare_thread.log_updated.connect(self.log_text.append)
        self.product_compare_thread.finished.connect(self.product_compare_finished)
        self.product_compare_thread.error.connect(self.product_compare_error)
        self.product_compare_thread.start()

    def update_compare_progress(self, current_page, total_pages):
        """更新商品对比检查进度"""
        if total_pages > 0:
            progress = int((current_page / total_pages) * 100)
            self.progress_bar.setValue(progress)
            self.log_text.append(f"正在处理第 {current_page}/{total_pages} 页")

    def product_compare_finished(self, result):
        """商品对比检查完成"""
        self.progress_bar.setValue(100)

        if 'error' in result:
            self.log_text.append(f"检查失败: {result['error']}")
            self.statusBar.showMessage("商品对比检查失败", 5000)
        else:
            # 显示基本结果
            self.log_text.append("=" * 50)
            self.log_text.append("商品对比检查完成！")
            self.log_text.append(f"线上商品数量: {result['线上商品数量']}")
            self.log_text.append(f"本地文件夹数量: {result['本地文件夹数量']}")
            self.log_text.append(f"已匹配数量: {result['已匹配数量']}")
            self.log_text.append(f"线下有线上无数量: {result['线下有线上无数量']}")
            self.log_text.append(f"线上有线下无数量: {result['线上有线下无数量']} (已打印到控制台)")
            self.log_text.append("=" * 50)

            # 如果有需要移动的文件夹，弹窗确认
            if result['线下有线上无数量'] > 0:
                self.show_move_confirmation_dialog(result)
            else:
                self.log_text.append("没有需要移动的文件夹")
                self.statusBar.showMessage(f"检查完成: 匹配{result['已匹配数量']}个，无需移动文件夹")

        # 重新启用按钮
        self.product_compare_button.setEnabled(True)
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        self.batch_modify_repost_button.setEnabled(True)
        self.product_collector_button.setEnabled(True)

    def show_move_confirmation_dialog(self, result):
        """显示移动确认对话框"""
        # 构建详细信息文本
        详细信息 = []

        # 已匹配的商品
        if result['对比详情']['已匹配']:
            详细信息.append("✅ 已匹配的商品:")
            for item in result['对比详情']['已匹配'][:5]:  # 显示前5个
                详细信息.append(f"   📁 {item['文件夹']} ➜ 🛒 {item['商品名称']}")
            if len(result['对比详情']['已匹配']) > 5:
                详细信息.append(f"   ... 还有 {len(result['对比详情']['已匹配']) - 5} 个已匹配项")
            详细信息.append("")

        # 线上有线下无的商品
        if result['对比详情']['线上有线下无']:
            详细信息.append("⚠️ 线上有但线下没有的商品 (已打印到控制台):")
            for product in result['对比详情']['线上有线下无'][:3]:  # 显示前3个
                详细信息.append(f"   🛒 {product}")
            if len(result['对比详情']['线上有线下无']) > 3:
                详细信息.append(f"   ... 还有 {len(result['对比详情']['线上有线下无']) - 3} 个线上有线下无项")
            详细信息.append("")

        # 需要移动的文件夹
        详细信息.append("❌ 线下有但线上没有的文件夹 (将移动到二次待发布):")
        for folder in result['对比详情']['线下有线上无'][:10]:  # 显示前10个
            详细信息.append(f"   📁 {folder}")
        if len(result['对比详情']['线下有线上无']) > 10:
            详细信息.append(f"   ... 还有 {len(result['对比详情']['线下有线上无']) - 10} 个需要移动的文件夹")

        详细信息_文本 = "\n".join(详细信息)

        # 创建确认对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("商品对比检查结果")
        msg_box.setIcon(QMessageBox.Icon.Question)

        # 设置主要文本
        主要文本 = f"""商品对比检查完成！

📊 统计结果:
• 线上商品数量: {result['线上商品数量']}
• 本地文件夹数量: {result['本地文件夹数量']}
• 已匹配数量: {result['已匹配数量']}
• 线下有线上无: {result['线下有线上无数量']} 个
• 线上有线下无: {result['线上有线下无数量']} 个

⚠️ 发现 {result['线下有线上无数量']} 个文件夹在线上没有对应商品，
这些文件夹将被移动到"二次待发布"文件夹中。

是否确定执行移动操作？"""

        msg_box.setText(主要文本)
        msg_box.setDetailedText(详细信息_文本)

        # 添加按钮
        确定按钮 = msg_box.addButton("确定移动", QMessageBox.ButtonRole.AcceptRole)
        取消按钮 = msg_box.addButton("取消", QMessageBox.ButtonRole.RejectRole)

        # 设置默认按钮
        msg_box.setDefaultButton(取消按钮)

        # 显示对话框并获取结果
        msg_box.exec()

        if msg_box.clickedButton() == 确定按钮:
            # 用户确认移动，执行移动操作
            self.execute_move_operation(result)
        else:
            # 用户取消，只显示结果不移动
            self.log_text.append("用户取消了移动操作")
            self.show_detailed_results(result, moved=False)
            self.statusBar.showMessage(f"检查完成: 匹配{result['已匹配数量']}个，用户取消移动操作")

    def execute_move_operation(self, result):
        """执行移动操作"""
        from 功能模块.商品对比检查 import 商品对比检查器

        self.log_text.append("开始执行移动操作...")

        try:
            # 创建检查器实例来执行移动
            checker = 商品对比检查器(log_func=self.log_text.append)
            移动结果 = checker.移动未匹配文件夹(result['对比详情']['线下有线上无'])

            # 更新结果
            result['成功移动'] = 移动结果['成功移动']
            result['移动失败'] = 移动结果['移动失败']

            # 显示移动结果
            self.log_text.append(f"移动操作完成: 成功移动 {移动结果['成功移动']} 个文件夹")
            if 移动结果['移动失败'] > 0:
                self.log_text.append(f"移动失败: {移动结果['移动失败']} 个文件夹")

            # 显示详细结果
            self.show_detailed_results(result, moved=True)
            self.statusBar.showMessage(f"检查完成: 匹配{result['已匹配数量']}个，移动{移动结果['成功移动']}个到二次待发布")

        except Exception as e:
            self.log_text.append(f"移动操作失败: {str(e)}")
            self.statusBar.showMessage("移动操作失败", 5000)

    def show_detailed_results(self, result, moved=False):
        """显示详细结果"""
        # 显示详细匹配信息
        if result['对比详情']['已匹配']:
            self.log_text.append("\n✅ 已匹配的商品:")
            for item in result['对比详情']['已匹配'][:10]:  # 只显示前10个
                self.log_text.append(f"  📁 {item['文件夹']} ➜ 🛒 {item['商品名称']}")
            if len(result['对比详情']['已匹配']) > 10:
                self.log_text.append(f"  ... 还有 {len(result['对比详情']['已匹配']) - 10} 个已匹配项")

        if result['对比详情']['线下有线上无']:
            状态文本 = "已移动到二次待发布" if moved else "未移动"
            self.log_text.append(f"\n❌ 线下有线上无的文件夹 ({状态文本}):")
            for folder in result['对比详情']['线下有线上无'][:10]:  # 只显示前10个
                self.log_text.append(f"  📁 {folder}")
            if len(result['对比详情']['线下有线上无']) > 10:
                self.log_text.append(f"  ... 还有 {len(result['对比详情']['线下有线上无']) - 10} 个线下有线上无项")

        if result['对比详情']['线上有线下无']:
            self.log_text.append(f"\n⚠️ 线上有线下无的商品 (已打印到控制台):")
            for product in result['对比详情']['线上有线下无'][:5]:  # 只显示前5个
                self.log_text.append(f"  🛒 {product}")
            if len(result['对比详情']['线上有线下无']) > 5:
                self.log_text.append(f"  ... 还有 {len(result['对比详情']['线上有线下无']) - 5} 个线上有线下无项")

    def product_compare_error(self, error_message):
        """商品对比检查错误处理"""
        QMessageBox.critical(self, "错误", f"商品对比检查过程中发生错误：{error_message}")
        self.log_text.append(f"商品对比检查错误: {error_message}")

        # 重新启用按钮
        self.product_compare_button.setEnabled(True)
        self.collect_button.setEnabled(True)
        self.polish_button.setEnabled(True)
        self.specified_polish_button.setEnabled(True)
        self.collect_game_button.setEnabled(True)
        self.clean_game_name_button.setEnabled(True)
        self.export_button.setEnabled(True)
        self.batch_delist_button.setEnabled(True)
        self.batch_modify_repost_button.setEnabled(True)
        self.product_collector_button.setEnabled(True)

        self.statusBar.showMessage("商品对比检查失败", 5000)

def main():
    try:
        logger.info("程序启动")
        
        # 检查并创建必要的文件夹
        config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "配置文件")
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "===输出文件===")
        
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
            logger.info(f"创建配置文件夹: {config_dir}")
            
            # 创建默认配置文件
            default_config = {
                "认证信息": {
                    "Authorization": "Bearer YOUR_AUTH_TOKEN",
                    "Cookie": "YOUR_COOKIE_VALUE"
                },
                "其他配置": {
                    "擦亮时间": "16:00"
                }
            }
            
            with open(os.path.join(config_dir, "商品配置.json"), 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=4)
                logger.info("创建默认配置文件")
        
        # 创建输出文件夹
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"创建输出文件夹: {output_dir}")
        
        app = QApplication(sys.argv)
        
        # 设置应用样式
        app.setStyle("Fusion")
        
        window = MainWindow()
        window.show()
        logger.info("主窗口已显示")
        sys.exit(app.exec())
    except Exception as e:
        logger.error(f"程序发生错误: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        QMessageBox.critical(None, "错误", f"程序启动失败：{str(e)}\n\n详细信息已记录到debug.log")
        sys.exit(1)

if __name__ == "__main__":
    main() 