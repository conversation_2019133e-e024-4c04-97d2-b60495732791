#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试两级优化逻辑
"""

def 模拟两级优化逻辑(game_name, title_template):
    """
    模拟新的两级优化逻辑
    """
    print(f"=" * 80)
    print(f"测试游戏: {game_name}")
    print(f"=" * 80)
    
    # 生成初始商品标题
    title = title_template.replace("[游戏名字]", game_name)
    print(f"初始完整标题: {title}")
    
    # 检测标题字节数
    title_bytes = title.encode('utf-8')
    actual_title_length = len(title_bytes)
    title_length = actual_title_length + 13  # 加上13个额外字节
    
    print(f"初始标题实际字节数: {actual_title_length}")
    print(f"初始标题计算用字节数(+13): {title_length}")
    
    # 如果标题大于等于90个字节，进行优化（但绝对不允许修改游戏名字）
    if title_length >= 90:
        print(f"\n🔧 标题计算用字节数大于等于90个字节，开始进行优化")
        
        # 第一级优化：删除"兑换码入库"
        print(f"\n第一级优化：删除'兑换码入库'")
        title = title.replace("兑换码入库", "")
        title_bytes = title.encode('utf-8')
        actual_new_title_length = len(title_bytes)
        new_title_length = actual_new_title_length + 13
        print(f"优化后的商品标题: {title}")
        print(f"优化后的标题字节数(+13): {new_title_length}")
        
        # 如果第一级优化后仍大于等于90字节，进行第二级优化
        if new_title_length >= 90:
            print(f"\n第二级优化：删除'steam激活码cdkey全DLC'")
            title = title.replace("steam激活码cdkey全DLC", "steam激活码")
            title_bytes = title.encode('utf-8')
            actual_new_title_length = len(title_bytes)
            new_title_length = actual_new_title_length + 13
            print(f"优化后的商品标题: {title}")
            print(f"优化后的标题字节数(+13): {new_title_length}")
        
        # 如果第二级优化后仍大于等于90字节，拒绝发布（绝对不允许修改游戏名字）
        if new_title_length >= 90:
            print(f"\n❌ 经过两级优化后标题计算用字节数({new_title_length})仍大于等于90个字节")
            print(f"❌ 游戏名字绝对不允许修改，拒绝发布: {game_name}")
            print(f"❌ 最终优化后标题: {title}")
            print(f"❌ 处理结果: 移动到发布失败文件夹")
            return False, "经过两级优化后仍然过长"
        else:
            print(f"\n✅ 经过优化后标题长度符合要求")
            print(f"✅ 最终标题: {title}")
            print(f"✅ 处理结果: 正常发布")
            return True, "优化后可以发布"
    else:
        print(f"\n✅ 标题长度符合要求，无需优化")
        print(f"✅ 处理结果: 直接发布")
        return True, "直接发布"

def 测试各种游戏名称():
    """测试各种长度的游戏名称"""
    print("开始测试两级优化逻辑")
    print("规则: 先删除'兑换码入库'，再删除'steam激活码cdkey全DLC'，但绝对不允许修改游戏名字")
    print()
    
    title_template = "[游戏名字] steam激活码cdkey全DLC单机PC游戏兑换码入库"
    
    测试用例 = [
        # 短名称（应该直接通过）
        "GTA5",
        "我的世界",
        
        # 中等长度（可能需要一级优化）
        "赛博朋克2077",
        "塞尔达传说 王国之泪",
        "最终幻想VII 重制版",
        
        # 较长名称（可能需要二级优化）
        "怪物猎人 世界 冰原",
        "上古卷轴5 天际 特别版",
        "巫师3 狂猎 年度版",
        
        # 很长名称（可能优化后仍然过长）
        "勇者斗恶龙 创世小玩家 阿雷夫加尔德复兴记",
        "最终幻想VII 重制版 完整版 豪华版 数字豪华版",
        "超级马里奥兄弟 奇迹 完整版 豪华版 数字豪华版 包含所有DLC",
    ]
    
    直接发布 = 0
    一级优化后发布 = 0
    二级优化后发布 = 0
    拒绝发布 = 0
    
    for game_name in 测试用例:
        # 记录优化前的状态
        original_title = title_template.replace("[游戏名字]", game_name)
        original_length = len(original_title.encode('utf-8')) + 13
        
        success, reason = 模拟两级优化逻辑(game_name, title_template)
        
        # 分析优化级别
        if original_length < 90:
            直接发布 += 1
            print(f"📊 分类: 直接发布")
        elif success and "兑换码入库" not in reason:
            if "steam激活码cdkey全DLC" in original_title:
                二级优化后发布 += 1
                print(f"📊 分类: 二级优化后发布")
            else:
                一级优化后发布 += 1
                print(f"📊 分类: 一级优化后发布")
        elif success:
            一级优化后发布 += 1
            print(f"📊 分类: 一级优化后发布")
        else:
            拒绝发布 += 1
            print(f"📊 分类: 拒绝发布")
        
        print()
    
    print("=" * 80)
    print("测试结果统计")
    print("=" * 80)
    print(f"总测试数量: {len(测试用例)}")
    print(f"直接发布: {直接发布}")
    print(f"一级优化后发布: {一级优化后发布}")
    print(f"二级优化后发布: {二级优化后发布}")
    print(f"拒绝发布: {拒绝发布}")
    print()
    print("✅ 优化策略优势:")
    print("   1. 保留有用的优化：删除冗余文字")
    print("   2. 保护游戏名字：绝对不允许修改")
    print("   3. 分级处理：逐步优化，最大化发布成功率")
    print("   4. 明确边界：优化后仍过长则拒绝发布")

def 详细分析优化效果():
    """详细分析优化效果"""
    print("\n" + "=" * 80)
    print("详细分析优化效果")
    print("=" * 80)
    
    title_template = "[游戏名字] steam激活码cdkey全DLC单机PC游戏兑换码入库"
    
    # 分析模板各部分的字节数
    game_placeholder = "[游戏名字]"
    template_parts = {
        "游戏名字占位符": game_placeholder,
        "前缀空格": " ",
        "steam激活码": "steam激活码",
        "cdkey全DLC": "cdkey全DLC", 
        "单机PC游戏": "单机PC游戏",
        "兑换码入库": "兑换码入库"
    }
    
    print("标题模板各部分字节数分析:")
    total_template_bytes = 0
    for part_name, part_text in template_parts.items():
        if part_name != "游戏名字占位符":
            bytes_count = len(part_text.encode('utf-8'))
            total_template_bytes += bytes_count
            print(f"  {part_name}: '{part_text}' = {bytes_count} 字节")
    
    print(f"\n模板总字节数（不含游戏名字）: {total_template_bytes}")
    print(f"加上额外13字节: {total_template_bytes + 13}")
    print(f"游戏名字可用字节数: {90 - total_template_bytes - 13} 字节")
    
    # 分析优化效果
    兑换码入库_bytes = len("兑换码入库".encode('utf-8'))
    cdkey全DLC_bytes = len("cdkey全DLC".encode('utf-8'))
    
    print(f"\n优化效果分析:")
    print(f"删除'兑换码入库'可节省: {兑换码入库_bytes} 字节")
    print(f"删除'cdkey全DLC'可节省: {cdkey全DLC_bytes} 字节")
    print(f"两级优化总共可节省: {兑换码入库_bytes + cdkey全DLC_bytes} 字节")
    
    # 计算优化后的游戏名字可用空间
    一级优化后模板字节 = total_template_bytes - 兑换码入库_bytes
    二级优化后模板字节 = 一级优化后模板字节 - cdkey全DLC_bytes
    
    print(f"\n游戏名字可用空间:")
    print(f"原始模板: {90 - total_template_bytes - 13} 字节")
    print(f"一级优化后: {90 - 一级优化后模板字节 - 13} 字节")
    print(f"二级优化后: {90 - 二级优化后模板字节 - 13} 字节")

def 主函数():
    """主测试函数"""
    try:
        测试各种游戏名称()
        详细分析优化效果()
        
        print("\n" + "=" * 80)
        print("两级优化逻辑测试完成")
        print("=" * 80)
        print("🎯 核心原则:")
        print("   1. 游戏名字绝对不允许修改")
        print("   2. 通过删除冗余文字进行优化")
        print("   3. 分级处理，最大化发布成功率")
        print("   4. 优化后仍过长则拒绝发布")
        print()
        print("🔧 优化策略:")
        print("   第一级：删除'兑换码入库'")
        print("   第二级：删除'steam激活码cdkey全DLC'改为'steam激活码'")
        print("   第三级：拒绝发布（不修改游戏名字）")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    主函数()
