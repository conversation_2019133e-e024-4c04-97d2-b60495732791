import requests
import json
import os
import shutil
from typing import List, Dict, Optional, Callable
import re
from pathlib import Path

class 商品对比检查器:
    """
    商品对比检查功能模块
    - 从API获取已发布商品列表
    - 与本地发布成功文件夹进行对比
    - 将未成功发布的商品移动到二次待发布文件夹
    """
    
    def __init__(self, log_func: Optional[Callable[[str], None]] = None):
        self.log_func = log_func if log_func else print
        self.api_url = "https://aldsidle.agiso.com/api/GoodsManage/SearchGoodsList"
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
        }
        self.发布成功路径 = "发布成功"
        self.二次待发布路径 = "二次待发布"
        
    def 设置请求头(self, headers: Dict[str, str]):
        """设置自定义请求头"""
        self.headers.update(headers)
        self.log_func(f"已更新请求头")
        
    def 获取所有商品名称(self, progress_callback: Optional[Callable[[int, int], None]] = None) -> List[str]:
        """
        获取所有页面的商品名称
        递增提取全部页面，直到提取不到数据为止
        """
        所有商品名称 = []
        当前页 = 1
        总页数 = None  # 初始时不知道总页数
        总条数 = None

        try:
            while True:  # 持续循环直到没有数据
                self.log_func(f"正在获取第 {当前页} 页商品信息...")

                # 构建请求数据
                请求数据 = {
                    "pageSize": 100,
                    "page": 当前页,
                    "categoryId": "",
                    "status": "0"
                }

                # 发送请求
                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=请求数据,
                    timeout=30
                )

                if response.status_code != 200:
                    self.log_func(f"请求失败，状态码: {response.status_code}")
                    break

                # 解析响应
                响应数据 = response.json()

                if 'data' not in 响应数据 or 'data' not in 响应数据['data']:
                    self.log_func(f"响应数据格式错误: {响应数据}")
                    break

                商品数据 = 响应数据['data']['data']

                # 获取总页数信息（仅在第一页时）
                if 当前页 == 1:
                    总条数 = 商品数据.get('total', 0)
                    每页条数 = 商品数据.get('pageSize', 100)
                    if 总条数 > 0 and 每页条数 > 0:
                        总页数 = (总条数 + 每页条数 - 1) // 每页条数
                        self.log_func(f"总共 {总条数} 个商品，预计分 {总页数} 页")
                    else:
                        self.log_func(f"无法确定总页数，将持续获取直到没有数据")

                # 提取商品名称
                商品列表 = 商品数据.get('items', [])

                # 如果当前页没有商品，说明已经到达最后一页
                if not 商品列表:
                    self.log_func(f"第 {当前页} 页没有商品数据，停止获取")
                    break

                # 添加商品名称
                当前页商品数 = 0
                for 商品 in 商品列表:
                    商品名称 = 商品.get('goodsName', '')
                    if 商品名称:
                        所有商品名称.append(商品名称)
                        当前页商品数 += 1

                self.log_func(f"第 {当前页} 页获取到 {当前页商品数} 个有效商品")

                # 更新进度
                if progress_callback:
                    if 总页数:
                        progress_callback(当前页, 总页数)
                    else:
                        # 如果不知道总页数，就传递当前页和当前页+1
                        progress_callback(当前页, 当前页 + 1)

                # 如果当前页商品数少于每页大小，说明这是最后一页
                if len(商品列表) < 100:
                    self.log_func(f"第 {当前页} 页商品数({len(商品列表)})少于每页大小(100)，这是最后一页")
                    break

                当前页 += 1

                # 安全检查：防止无限循环
                if 当前页 > 1000:  # 假设最多1000页
                    self.log_func(f"已达到最大页数限制(1000页)，停止获取")
                    break

        except Exception as e:
            self.log_func(f"获取商品信息时发生错误: {str(e)}")

        self.log_func(f"总共获取到 {len(所有商品名称)} 个商品名称，实际页数: {当前页}")
        return 所有商品名称
        
    def 获取本地文件夹列表(self) -> List[str]:
        """获取发布成功文件夹中的所有子文件夹名称"""
        本地文件夹列表 = []
        
        if not os.path.exists(self.发布成功路径):
            self.log_func(f"发布成功文件夹不存在: {self.发布成功路径}")
            return 本地文件夹列表
            
        try:
            for 项目 in os.listdir(self.发布成功路径):
                项目路径 = os.path.join(self.发布成功路径, 项目)
                if os.path.isdir(项目路径):
                    本地文件夹列表.append(项目)
                    
            self.log_func(f"发布成功文件夹中找到 {len(本地文件夹列表)} 个子文件夹")
            
        except Exception as e:
            self.log_func(f"读取本地文件夹时发生错误: {str(e)}")
            
        return 本地文件夹列表
        
    def 对比商品(self, 线上商品列表: List[str], 本地文件夹列表: List[str]) -> Dict[str, List[str]]:
        """
        对比线上商品和本地文件夹
        直接进行关键词匹配，不使用智能清理
        """
        匹配结果 = {
            '已匹配': [],
            '线下有线上无': [],  # 线下有但线上没有的，需要移动到二次待发布
            '线上有线下无': []   # 线上有但线下没有的，打印到控制台
        }

        self.log_func(f"开始对比 {len(本地文件夹列表)} 个本地文件夹...")

        # 1. 检查本地文件夹在线上商品中的匹配情况
        for 文件夹名称 in 本地文件夹列表:
            匹配到 = False

            # 检查文件夹名称是否包含在任何线上商品名称中
            for 商品名称 in 线上商品列表:
                if 文件夹名称 in 商品名称:
                    匹配结果['已匹配'].append({
                        '文件夹': 文件夹名称,
                        '商品名称': 商品名称
                    })
                    匹配到 = True
                    break

            if not 匹配到:
                匹配结果['线下有线上无'].append(文件夹名称)

        # 2. 检查线上商品在本地文件夹中的匹配情况
        for 商品名称 in 线上商品列表:
            匹配到 = False

            # 检查是否有本地文件夹名称包含在商品名称中
            for 文件夹名称 in 本地文件夹列表:
                if 文件夹名称 in 商品名称:
                    匹配到 = True
                    break

            if not 匹配到:
                匹配结果['线上有线下无'].append(商品名称)
                # 直接打印到控制台
                print(f"线上有但线下没有的商品: {商品名称}")

        self.log_func(f"对比完成: 已匹配 {len(匹配结果['已匹配'])} 个")
        self.log_func(f"线下有线上无 {len(匹配结果['线下有线上无'])} 个 (将移动到二次待发布)")
        self.log_func(f"线上有线下无 {len(匹配结果['线上有线下无'])} 个 (已打印到控制台)")

        return 匹配结果
        
    def 移动未匹配文件夹(self, 线下有线上无列表: List[str]) -> Dict[str, int]:
        """
        将线下有但线上没有的文件夹移动到二次待发布文件夹
        """
        结果统计 = {
            '成功移动': 0,
            '移动失败': 0
        }

        if not 线下有线上无列表:
            self.log_func("没有需要移动的文件夹")
            return 结果统计

        # 确保二次待发布文件夹存在
        os.makedirs(self.二次待发布路径, exist_ok=True)

        for 文件夹名称 in 线下有线上无列表:
            try:
                源路径 = os.path.join(self.发布成功路径, 文件夹名称)
                目标路径 = os.path.join(self.二次待发布路径, 文件夹名称)

                # 如果目标路径已存在，添加序号
                计数器 = 1
                原始目标路径 = 目标路径
                while os.path.exists(目标路径):
                    目标路径 = f"{原始目标路径}_{计数器}"
                    计数器 += 1

                # 移动文件夹
                shutil.move(源路径, 目标路径)
                self.log_func(f"已移动: {文件夹名称} -> {self.二次待发布路径}")
                结果统计['成功移动'] += 1

            except Exception as e:
                self.log_func(f"移动文件夹失败 {文件夹名称}: {str(e)}")
                结果统计['移动失败'] += 1

        return 结果统计
        
    def 执行检查(self, progress_callback: Optional[Callable[[int, int], None]] = None) -> Dict:
        """
        执行完整的商品对比检查流程
        """
        try:
            # 1. 获取线上商品列表
            self.log_func("开始获取线上商品列表...")
            线上商品列表 = self.获取所有商品名称(progress_callback)
            
            if not 线上商品列表:
                return {'error': '未能获取到线上商品信息'}
                
            # 2. 获取本地文件夹列表
            self.log_func("获取本地文件夹列表...")
            本地文件夹列表 = self.获取本地文件夹列表()
            
            if not 本地文件夹列表:
                return {'error': '发布成功文件夹为空或不存在'}
                
            # 3. 对比商品
            self.log_func("开始对比商品...")
            对比结果 = self.对比商品(线上商品列表, 本地文件夹列表)
            
            # 4. 不自动移动文件夹，返回对比结果供界面处理
            self.log_func(f"对比检查完成，等待用户确认是否移动文件夹...")

            # 5. 返回完整结果（不包含移动结果）
            return {
                'success': True,
                '线上商品数量': len(线上商品列表),
                '本地文件夹数量': len(本地文件夹列表),
                '已匹配数量': len(对比结果['已匹配']),
                '线下有线上无数量': len(对比结果['线下有线上无']),
                '线上有线下无数量': len(对比结果['线上有线下无']),
                '成功移动': 0,  # 初始值，移动操作由界面单独执行
                '移动失败': 0,  # 初始值，移动操作由界面单独执行
                '对比详情': 对比结果
            }
            
        except Exception as e:
            error_msg = f"执行检查时发生错误: {str(e)}"
            self.log_func(error_msg)
            return {'error': error_msg}
