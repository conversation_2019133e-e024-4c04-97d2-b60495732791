import os
import json
import time
import requests
import logging
import random
from typing import Dict, List, Tuple, Union, Optional

class BatchModifyRepost:
    """批量修改重发商品类"""
    
    def __init__(self, config_path, log_func=None):
        """
        初始化批量修改重发器
        
        Args:
            config_path: 配置文件路径
            log_func: 日志输出函数
        """
        self.config_path = config_path
        self.log_func = log_func or print
        self.auth_info = self._load_auth_info()
        self.get_item_url = "https://aldsidle.agiso.com/api/GoodsManage/GetItemInfo"
        self.edit_item_url = "https://aldsidle.agiso.com/api/GoodsManage/Edit"
        self.running = True
        
        # 定义可替换的描述文本列表
        self.desc_replacements = [
            "激活完成后，下载游戏就能开始玩了！",
            "成功激活后，直接下载安装即可畅玩！",
            "激活搞定，下载好游戏就能嗨起来啦！",
            "完成激活步骤后，下载入库就能体验游戏了！",
            "激活成功之后入库下载就可以玩啦"
        ]
        
    def _load_auth_info(self):
        """加载认证信息"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            auth_info = config.get("认证信息", {})
            if not auth_info.get("Authorization") or not auth_info.get("Cookie"):
                self.log_func("警告：认证信息不完整，请检查配置文件")
                
            return auth_info
        except Exception as e:
            self.log_func(f"加载认证信息失败：{str(e)}")
            return {}
            
    def stop(self):
        """停止修改重发过程"""
        self.running = False
        self.log_func("正在停止批量修改重发...")
    
    def get_item_info(self, goods_id) -> Tuple[bool, Union[Dict, str]]:
        """
        获取商品信息
        
        Args:
            goods_id: 商品ID
            
        Returns:
            (bool, Union[Dict, str]): (是否成功, 商品信息或错误信息)
        """
        try:
            # 构建请求头
            headers = {
                "accept": "application/json, text/plain, */*",
                "authorization": self.auth_info.get("Authorization", ""),
                "content-type": "application/json;charset=UTF-8",
                "origin": "https://aldsidle.agiso.com",
                "referer": "https://aldsidle.agiso.com/",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            }
            
            # 解析Cookie
            cookies = {}
            cookie_string = self.auth_info.get("Cookie", "")
            for item in cookie_string.split(';'):
                if '=' in item:
                    name, value = item.strip().split('=', 1)
                    cookies[name] = value
            
            # 构建请求数据
            data = {
                "goodsId": goods_id,
                "draft": False,
                "virtualGameGoods": False
            }
            
            # 记录请求信息到日志（不打印到控制台）
            if self.log_func:
                self.log_func(f"获取商品信息: {goods_id}")
            
            # 发送请求
            response = requests.post(
                self.get_item_url,
                headers=headers,
                cookies=cookies,
                json=data,
                timeout=30
            )
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                
                if result.get('succeeded'):
                    if 'data' in result and 'data' in result['data']:
                        return True, result['data']['data']
                    else:
                        return False, "响应格式不正确"
                else:
                    error_msg = result.get('message', '未知错误')
                    return False, error_msg
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            if self.log_func:
                self.log_func(f"获取商品信息异常: {str(e)}")
            return False, f"请求异常: {str(e)}"
    
    def modify_and_repost(self, goods_id) -> Tuple[bool, str]:
        """
        修改并重新发布商品
        
        Args:
            goods_id: 商品ID
            
        Returns:
            (bool, str): (是否成功, 错误信息)
        """
        try:
            # 获取商品信息
            self.log_func(f"正在获取商品信息: {goods_id}")
            success, result = self.get_item_info(goods_id)
            
            if not success:
                return False, f"获取商品信息失败: {result}"
            
            # 修改商品信息
            self.log_func("正在修改商品信息...")
            item_data = result
            
            # 调整最后两张图片的位置
            if 'imgList' in item_data and len(item_data['imgList']) >= 4:
                # 交换最后两张图片
                item_data['imgList'][-1], item_data['imgList'][-2] = item_data['imgList'][-2], item_data['imgList'][-1]
                
                # 同时更新imgUrls数组
                if 'imgUrls' in item_data and len(item_data['imgUrls']) >= 4:
                    item_data['imgUrls'][-1], item_data['imgUrls'][-2] = item_data['imgUrls'][-2], item_data['imgUrls'][-1]
                    
                self.log_func("已调整最后两张图片位置")
            else:
                self.log_func(f"警告: 商品图片数量不足，无法调整位置 (图片数量: {len(item_data.get('imgList', []))})")
            
            # 修改商品描述内容
            if 'desc' in item_data and item_data['desc']:
                original_desc = item_data['desc']
                modified_desc = original_desc
                
                # 查找是否包含可替换的文本
                found_text = None
                for text in self.desc_replacements:
                    if text in original_desc:
                        found_text = text
                        break
                
                # 如果找到了可替换的文本，则随机选择一个不同的文本进行替换
                if found_text:
                    # 创建一个不包含当前文本的替换列表
                    available_replacements = [text for text in self.desc_replacements if text != found_text]
                    
                    if available_replacements:
                        # 随机选择一个替换文本
                        replacement_text = random.choice(available_replacements)
                        
                        # 替换文本
                        modified_desc = original_desc.replace(found_text, replacement_text)
                        
                        self.log_func(f"已替换描述文本: '{found_text}' -> '{replacement_text}'")
                    else:
                        self.log_func("没有可用的替换文本")
                else:
                    self.log_func("描述中未找到可替换的文本")
                
                # 更新商品描述
                item_data['desc'] = modified_desc
            else:
                self.log_func("商品没有描述信息，无法修改")
            
            # 确保transportFee是数字类型
            if 'transportFee' in item_data and isinstance(item_data['transportFee'], str):
                item_data['transportFee'] = float(item_data['transportFee'])
            
            # 添加freeShipping字段
            item_data['freeShipping'] = True
            
            # 添加categoryName字段
            if 'categoryName' not in item_data:
                item_data['categoryName'] = "其他/电玩/游戏软件/PS游戏光盘/软件"
            
            # 确保spBizType是数字类型
            if 'spBizType' in item_data and isinstance(item_data['spBizType'], str):
                item_data['spBizType'] = int(item_data['spBizType'])
            
            # 构建请求头
            headers = {
                "accept": "application/json, text/plain, */*",
                "authorization": self.auth_info.get("Authorization", ""),
                "content-type": "application/json;charset=UTF-8",
                "origin": "https://aldsidle.agiso.com",
                "referer": "https://aldsidle.agiso.com/",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            }
            
            # 解析Cookie
            cookies = {}
            cookie_string = self.auth_info.get("Cookie", "")
            for item in cookie_string.split(';'):
                if '=' in item:
                    name, value = item.strip().split('=', 1)
                    cookies[name] = value
            
            # 打印请求信息
            print(f"修改重发商品 - 请求URL: {self.edit_item_url}")
            print(f"修改重发商品 - 请求头: {headers}")
            print(f"修改重发商品 - 请求数据: {json.dumps(item_data, ensure_ascii=False)[:500]}...")
            
            # 发送请求
            response = requests.post(
                self.edit_item_url,
                headers=headers,
                cookies=cookies,
                json=item_data,
                timeout=30
            )
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                # 只在最终修改重发时打印响应信息到控制台
                print(f"修改重发商品最终响应: {json.dumps(result, ensure_ascii=False)}")
                
                if result.get('succeeded'):
                    if 'data' in result and 'isSuccess' in result['data'] and result['data']['isSuccess']:
                        return True, ""
                    else:
                        error_msg = result.get('data', {}).get('errorMsg', '未知错误')
                        return False, error_msg
                else:
                    error_msg = result.get('message', '未知错误')
                    return False, error_msg
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except Exception as e:
            if self.log_func:
                self.log_func(f"修改重发商品异常: {str(e)}")
            return False, f"请求异常: {str(e)}"
    
    def batch_modify_repost(self, goods_ids, progress_callback=None, status_callback=None):
        """
        批量修改重发商品
        
        Args:
            goods_ids: 商品ID列表
            progress_callback: 进度回调函数，参数为(当前进度, 总数量, 成功数, 失败数)
            status_callback: 状态回调函数，参数为(商品ID, 是否成功, 错误信息)
            
        Returns:
            dict: 包含总数、成功数、失败数和失败列表的字典
        """
        total = len(goods_ids)
        success_count = 0
        failed_count = 0
        failed_items = []
        self.running = True
        
        self.log_func(f"开始批量修改重发，共 {total} 个商品")
        
        for i, goods_id in enumerate(goods_ids):
            # 检查是否已停止
            if not self.running:
                self.log_func("批量修改重发已手动停止")
                break
                
            # 更新进度
            current = i + 1
            if progress_callback:
                progress_callback(current, total, success_count, failed_count)
            
            self.log_func(f"正在修改重发商品 ({current}/{total}): {goods_id}")
            
            # 修改重发商品
            success, error_msg = self.modify_and_repost(goods_id)
            
            # 更新状态
            if success:
                success_count += 1
                self.log_func(f"✓ 商品 {goods_id} 修改重发成功")
            else:
                failed_count += 1
                failed_items.append({"goods_id": goods_id, "error": error_msg})
                self.log_func(f"✗ 商品 {goods_id} 修改重发失败: {error_msg}")
            
            # 回调状态
            if status_callback:
                status_callback(goods_id, success, error_msg)
            
            # 短暂延迟，避免请求过快
            time.sleep(1)
        
        # 汇总结果
        result = {
            "total": total,
            "success": success_count,
            "failed": failed_count,
            "failed_items": failed_items
        }
        
        self.log_func(f"批量修改重发完成，共 {total} 个商品，成功 {success_count} 个，失败 {failed_count} 个")
        return result 