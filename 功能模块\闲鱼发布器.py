import requests
import json
import os
import shutil
import time
import threading
from datetime import datetime
from typing import List, Dict, Optional, Union, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

class XianYuError(Exception):
    """闲鱼发布器基础异常类"""
    pass

class ConfigError(XianYuError):
    """配置相关错误"""
    pass

class UploadError(XianYuError):
    """上传相关错误"""
    pass

class PublishError(XianYuError):
    """发布相关错误"""
    pass

class Stats:
    """统计信息类"""
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置统计信息"""
        self.total_folders = 0
        self.success_publish = 0
        self.failed_publish = 0
        self.total_images = 0
        self.success_upload = 0
        self.failed_upload = 0
        self.start_time = None
        self.end_time = None
        self.errors = []

class 闲鱼发布器:
    """闲鱼商品发布器核心类"""
    
    def __init__(self, config_path=None, log_func=None):
        """
        初始化发布器
        
        Args:
            config_path: 配置文件路径
            log_func: 日志输出函数
        """
        self.config = None
        self.stats = Stats()
        self.is_running = False
        self._lock = threading.Lock()
        self.log_func = log_func or print
        
        if config_path:
            self.load_config(config_path)
    
    def load_config(self, config_path: str) -> None:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Raises:
            ConfigError: 配置加载失败时抛出
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.log_func("配置加载成功")
        except Exception as e:
            error_msg = f"配置加载失败: {str(e)}"
            self.log_func(error_msg)
            raise ConfigError(error_msg)
    
    def _build_headers(self) -> Dict:
        """构建请求头"""
        if not self.config or "认证信息" not in self.config:
            raise ConfigError("未找到认证信息")
            
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Content-Type": "application/json",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Origin": "https://aldsidle.agiso.com",
            "Referer": "https://aldsidle.agiso.com/",
            "Authorization": self.config["认证信息"]["Authorization"]
        }
    
    def _parse_cookie(self) -> Dict:
        """解析Cookie字符串为字典"""
        if not self.config or "认证信息" not in self.config or "Cookie" not in self.config["认证信息"]:
            raise ConfigError("未找到Cookie信息")
            
        cookie_dict = {}
        for item in self.config["认证信息"]["Cookie"].split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookie_dict[key] = value
        return cookie_dict
    
    def upload_image(self, image_path: str) -> Tuple[bool, Union[Dict, str]]:
        """
        上传单张图片，包含重试机制
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            Tuple[bool, Union[Dict, str]]: (是否成功, 结果信息)
        """
        # 增强重试机制
        max_retries = 5  # 减少最大重试次数到5次
        retry_count = 0
        retry_delay = 3  # 固定重试间隔为3秒
        
        # 用于防止重复上传
        image_name = os.path.basename(image_path)
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return False, f"文件不存在: {image_path}"
        
        # 检查文件大小
        file_size = os.path.getsize(image_path)
        if file_size == 0:
            return False, f"文件大小为0: {image_path}"
            
        # 检查图片格式
        ext = os.path.splitext(image_path)[1].lower()
        if ext not in ['.jpg', '.jpeg', '.png']:
            return False, f"不支持的图片格式 {ext}: {image_path}"
        
        # 记录开始上传时间
        start_time = time.time()
        self.log_func(f"===== 开始上传图片: {image_name} (大小: {file_size} 字节) =====")
        
        while retry_count <= max_retries:
            try:
                headers = self._build_headers()
                if 'Content-Type' in headers:
                    del headers['Content-Type']
                
                cookies = self._parse_cookie()
                
                # 日志只显示简易信息
                retry_msg = f" (重试 {retry_count}/{max_retries})" if retry_count > 0 else ""
                self.log_func(f"上传图片{retry_msg}: {image_name}")
                
                with open(image_path, 'rb') as f:
                    files = {
                        'files': (
                            image_name,
                            f,
                            'image/jpeg' if image_path.lower().endswith('.jpg') else 'image/png'
                        )
                    }
                    
                    # 设置更长的超时时间
                    response = requests.post(
                        "https://aldsidle.agiso.com/api/GoodsManage/MediaUpload",
                        headers=headers,
                        cookies=cookies,
                        files=files,
                        timeout=120  # 增加超时时间到120秒
                    )
                
                # 记录响应时间
                response_time = time.time() - start_time
                self.log_func(f"图片 {image_name} 响应时间: {response_time:.2f}秒")

                if response.status_code == 200:
                    try:
                        result = response.json()
                        
                        if result.get('succeeded'):
                            # 检查响应结构
                            if 'data' in result and isinstance(result['data'], dict):
                                # 检查是否有错误信息
                                if 'isSuccess' in result['data'] and not result['data']['isSuccess']:
                                    error_msg = result['data'].get('errorMsg', '未知错误')
                                    
                                    # 如果提示"图片正在上传中"，延长等待时间
                                    if "图片正在上传中" in error_msg:
                                        retry_count += 1
                                        longer_delay = 5  # 延长到5秒
                                        if retry_count <= max_retries:
                                            self.log_func(f"服务器提示图片正在上传中，{longer_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                                            time.sleep(longer_delay)
                                            continue
                                        else:
                                            self.log_func(f"服务器一直提示图片正在上传中，已达到最大重试次数: {image_name}")
                                            return False, f"服务器一直提示图片正在上传中，已重试{max_retries}次"
                                    
                                    # 其他错误
                                    self.log_func(f"图片上传失败: {image_name} - {error_msg}")
                                    return False, f"上传失败: {error_msg}"
                                
                                # 检查标准成功结构
                                if 'data' in result['data'] and isinstance(result['data']['data'], dict):
                                    img_data = result['data']['data']
                                    self.log_func(f"✅ 图片上传成功: {image_name}")
                                    self.log_func(f"图片ID: {img_data.get('imageId', 'unknown')}")
                                    return True, img_data
                                else:
                                    # 非标准结构但isSuccess为True的情况
                                    if 'isSuccess' in result['data'] and result['data']['isSuccess']:
                                        # 尝试从不标准结构中提取需要的信息
                                        if 'data' in result['data']:
                                            img_data = result['data']['data']
                                            self.log_func(f"✅ 图片上传成功(非标准结构): {image_name}")
                                            return True, img_data
                                    
                                    # 任何非标准结构都进行重试
                                    retry_count += 1
                                    if retry_count <= max_retries:
                                        self.log_func(f"图片上传响应格式不标准，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                                        time.sleep(retry_delay)
                                        continue
                                    else:
                                        self.log_func(f"图片上传响应格式始终不标准: {image_name}")
                                        return False, "响应格式不是标准结构，上传失败"
                            else:
                                # 没有预期的data字段
                                retry_count += 1
                                if retry_count <= max_retries:
                                    self.log_func(f"图片上传响应缺少data字段，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                                    time.sleep(retry_delay)
                                    continue
                                else:
                                    self.log_func(f"图片上传响应始终缺少data字段: {image_name}")
                                    return False, "响应缺少data字段，上传失败"
                        else:
                            # 上传失败，检查是否有详细错误信息
                            error_msg = result.get('message', '未知错误')
                            
                            # 特定错误类型可能需要重试
                            if "rate limit" in error_msg.lower() or "too many requests" in error_msg.lower():
                                retry_count += 1
                                if retry_count <= max_retries:
                                    self.log_func(f"图片上传频率限制，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                                    time.sleep(retry_delay)
                                    continue
                            
                            self.log_func(f"图片上传失败: {image_name} - {error_msg}")
                            return False, f"上传失败: {error_msg}"
                    except json.JSONDecodeError:
                        # 响应不是有效的JSON
                        retry_count += 1
                        if retry_count <= max_retries:
                            self.log_func(f"图片上传JSON解析错误，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                            time.sleep(retry_delay)
                            continue
                        else:
                            self.log_func(f"图片上传JSON解析错误，已达到最大重试次数: {image_name}")
                            return False, "JSON解析错误，无法处理服务器响应"
                
                elif response.status_code == 401:
                    self.log_func(f"图片上传失败(401认证错误): {image_name}")
                    return False, f"请求失败，认证错误(401): 请检查Authorization和Cookie是否有效"
                
                elif response.status_code in [429, 503, 502, 500, 504]:
                    # 服务器限流、暂时不可用、服务器错误等，进行重试
                    retry_count += 1
                    if retry_count <= max_retries:
                        self.log_func(f"图片上传失败(状态码{response.status_code})，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.log_func(f"图片上传失败(状态码{response.status_code})，已达到最大重试次数: {image_name}")
                        return False, f"请求失败，服务器错误({response.status_code})"
                else:
                    # 其他错误状态码
                    retry_count += 1
                    if retry_count <= max_retries:
                        self.log_func(f"图片上传失败(状态码{response.status_code})，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.log_func(f"图片上传失败(状态码{response.status_code})，已达到最大重试次数: {image_name}")
                        return False, f"请求失败，状态码: {response.status_code}"
            
            except requests.RequestException as e:
                # 网络请求异常
                self.log_func(f"图片上传网络异常: {image_name} - {str(e)}")
                
                retry_count += 1
                if retry_count <= max_retries:
                    self.log_func(f"图片上传网络异常，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name}")
                    time.sleep(retry_delay)
                    continue
                else:
                    self.log_func(f"图片上传网络异常，已达到最大重试次数: {image_name}")
                    return False, f"网络请求异常: {str(e)}"
                    
            except Exception as e:
                # 其他未预期的异常
                
                retry_count += 1
                if retry_count <= max_retries:
                    self.log_func(f"图片上传异常，{retry_delay}秒后重试({retry_count}/{max_retries}): {image_name} - {str(e)}")
                    time.sleep(retry_delay)
                    continue
                else:
                    self.log_func(f"图片上传异常，已达到最大重试次数: {image_name} - {str(e)}")
                    return False, f"上传异常: {str(e)}"
                
        # 如果所有重试都失败
        self.log_func(f"❌ 图片上传失败，已达到最大重试次数: {image_name}")
        return False, f"上传失败，已达到最大重试次数{max_retries}"
    
    def upload_images(self, image_paths: List[str]) -> List[Dict]:
        """
        批量上传图片，确保每张图片都必须成功上传
        
        Args:
            image_paths: 图片路径列表
            
        Returns:
            List[Dict]: 成功上传的图片信息列表，如果有任何图片上传失败则返回空列表
        """
        if not image_paths:
            self.log_func("没有找到需要上传的图片")
            return []
        
        # 确保图片路径列表中没有重复项
        unique_image_paths = []
        seen_filenames = set()
        
        for path in image_paths:
            filename = os.path.basename(path)
            if filename not in seen_filenames:
                seen_filenames.add(filename)
                unique_image_paths.append(path)
            else:
                self.log_func(f"警告: 跳过重复图片 {filename}")
        
        if len(unique_image_paths) < len(image_paths):
            self.log_func(f"原始图片数量: {len(image_paths)}, 去重后图片数量: {len(unique_image_paths)}")
        
        image_paths = unique_image_paths
        total_images = len(image_paths)
        
        results = {}
        failed_images = []
        
        self.log_func(f"开始批量上传图片，共 {total_images} 张")
        
        # 降低并发数，减少服务器压力
        max_workers = min(2, total_images)  # 最多2个并发请求
        
        # 第一轮尝试：并发上传所有图片
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_info = {
                executor.submit(self.upload_image, path): (i, path)
                for i, path in enumerate(image_paths)
            }
            
            for future in as_completed(future_to_info):
                index, path = future_to_info[future]
                try:
                    success, result = future.result()
                    if success:
                        results[index] = result
                        with self._lock:
                            self.stats.success_upload += 1
                            self.log_func(f"✅ 图片上传成功 ({len(results)}/{total_images}): {os.path.basename(path)}")
                    else:
                        failed_images.append((index, path, result))
                        with self._lock:
                            self.stats.failed_upload += 1
                            self.log_func(f"❌ 图片上传失败 ({len(failed_images)} 失败/{total_images} 总数): {os.path.basename(path)} - {result}")
                except Exception as e:
                    failed_images.append((index, path, str(e)))
                    with self._lock:
                        self.stats.failed_upload += 1
                        self.log_func(f"⚠️ 图片上传异常 ({len(failed_images)} 失败/{total_images} 总数): {os.path.basename(path)} - {str(e)}")
                        
                # 每次上传完成后更新统计信息
                with self._lock:
                    self.stats.total_images = total_images
                    completed = len(results) + len(failed_images)
                    self.log_func(f"图片上传进度: {completed}/{total_images} ({len(results)} 成功, {len(failed_images)} 失败)")
        
        # 第二轮：顺序重试失败的图片，每张图片增加休眠时间
        if failed_images:
            self.log_func(f"第一轮上传后有 {len(failed_images)} 张图片失败，开始顺序重试...")
            retry_delay = 3  # 固定重试间隔为3秒
            max_retry_rounds = 5  # 最大重试轮数
            
            for retry_round in range(1, max_retry_rounds + 1):
                if not failed_images:
                    break  # 如果所有图片都已上传成功，则退出循环
                    
                remaining_fails = []
                self.log_func(f"第 {retry_round} 轮重试，间隔 {retry_delay} 秒，剩余 {len(failed_images)} 张图片")
                
                for idx, (index, path, error) in enumerate(failed_images):
                    self.log_func(f"重试 {idx+1}/{len(failed_images)}: {os.path.basename(path)}")
                    
                    try:
                        success, result = self.upload_image(path)
                        if success:
                            results[index] = result
                            with self._lock:
                                self.stats.success_upload += 1
                                self.stats.failed_upload -= 1  # 减去之前计入失败的统计
                                self.log_func(f"✅ 重试成功 ({retry_round}/{max_retry_rounds}): {os.path.basename(path)}")
                        else:
                            remaining_fails.append((index, path, result))
                            self.log_func(f"❌ 重试失败 ({retry_round}/{max_retry_rounds}): {os.path.basename(path)} - {result}")
                    except Exception as e:
                        remaining_fails.append((index, path, str(e)))
                        self.log_func(f"⚠️ 重试异常 ({retry_round}/{max_retry_rounds}): {os.path.basename(path)} - {str(e)}")
                    
                    # 每张图片之间增加固定延迟
                    if idx < len(failed_images) - 1:
                        self.log_func(f"等待 {retry_delay} 秒后继续下一张图片...")
                        time.sleep(retry_delay)
                
                # 更新失败列表
                failed_images = remaining_fails
                
                # 如果还有失败的图片，在下一轮重试前等待3秒
                if failed_images and retry_round < max_retry_rounds:
                    self.log_func(f"等待 {retry_delay} 秒后开始下一轮重试...")
                    time.sleep(retry_delay)
        
        # 最终检查是否所有图片都上传成功
        success_count = len(results)
        fail_count = total_images - success_count
        
        # 记录最终结果
        if fail_count > 0:
            self.log_func(f"⚠️ 图片上传最终完成，但有 {fail_count}/{total_images} 张图片上传失败")
            missing_indices = [i for i in range(total_images) if i not in results]
            self.log_func(f"上传失败的图片索引: {missing_indices}")
            
            # 如果有任何图片上传失败，返回空列表表示整体上传失败
            return []
        else:
            self.log_func(f"✅ 所有 {total_images} 张图片上传成功")
            
        # 返回排序后的结果
        return [results[i] for i in range(total_images) if i in results]
    
    def publish_goods(self, game_name: str, uploaded_images: List[Dict]) -> bool:
        """
        发布商品
        
        Args:
            game_name: 游戏名称
            uploaded_images: 已上传的图片信息列表
            
        Returns:
            bool: 是否发布成功
        """
        # 添加重试机制
        max_retries = 5  # 与图片上传保持一致
        retry_count = 0
        retry_delays = [3, 3, 3, 5, 10]  # 重试延迟时间，单位秒
        
        try:
            if not self.config:
                raise ConfigError("未加载配置")
            
            # 获取商品标题模板
            title_template = self.config["商品基本信息"]["商品标题模板"]
            self.log_func(f"商品标题模板: {title_template}")
            
            # 生成商品标题
            title = title_template.replace("[游戏名字]", game_name)
            self.log_func(f"组装后的完整商品标题: {title}")
            
            # 检测标题字节数（使用UTF-8编码，中文一般占3字节，英文占1字节）
            title_bytes = title.encode('utf-8')
            actual_title_length = len(title_bytes)
            # 根据产品特殊性，需要额外加上13个字节
            title_length = actual_title_length + 13
            self.log_func(f"商品标题实际字节数: {actual_title_length}")
            self.log_func(f"商品标题计算用字节数(+13): {title_length}")
            
            # 如果标题大于等于90个字节，进行优化（但绝对不允许修改游戏名字）
            if title_length >= 90:
                self.log_func(f"商品标题计算用字节数大于等于90个字节，开始进行优化")

                # 第一级优化：删除"兑换码入库"
                self.log_func(f"第一级优化：删除'兑换码入库'")
                title = title.replace("兑换码入库", "")
                title_bytes = title.encode('utf-8')
                actual_new_title_length = len(title_bytes)
                new_title_length = actual_new_title_length + 13
                self.log_func(f"优化后的商品标题: {title}")
                self.log_func(f"优化后的标题字节数(+13): {new_title_length}")

                # 如果第一级优化后仍大于等于90字节，进行第二级优化
                if new_title_length >= 90:
                    self.log_func(f"第二级优化：删除'steam激活码cdkey全DLC'")
                    title = title.replace("steam激活码cdkey全DLC", "steam激活码")
                    title_bytes = title.encode('utf-8')
                    actual_new_title_length = len(title_bytes)
                    new_title_length = actual_new_title_length + 13
                    self.log_func(f"优化后的商品标题: {title}")
                    self.log_func(f"优化后的标题字节数(+13): {new_title_length}")

                # 如果第二级优化后仍大于等于90字节，拒绝发布（绝对不允许修改游戏名字）
                if new_title_length >= 90:
                    self.log_func(f"❌ 经过两级优化后标题计算用字节数({new_title_length})仍大于等于90个字节")
                    self.log_func(f"❌ 游戏名字绝对不允许修改，拒绝发布: {game_name}")
                    self.log_func(f"❌ 最终优化后标题: {title}")
                    error_msg = f"商品标题经过优化后仍然过长，游戏名字不允许修改，拒绝发布: {game_name}"
                    self.log_func(error_msg)
                    return False
            
            # 从配置文件中获取所有需要的配置项
            # 注意: 所有这些配置项都可以在配置文件中修改，无需修改代码
            商品分类信息 = self.config["商品分类信息"]  # 包含分类ID、名称、属性等
            地区信息 = self.config["地区信息"]  # 包含发货地区ID等
            商品状态配置 = self.config["商品状态配置"]  # 包含商品状态、交易类型等
            售后配置 = self.config["售后配置"]  # 包含售后政策配置
            
            # 构建发布商品的请求数据
            # 所有数据都从配置文件中读取，方便随时修改
            payload = {
                "id": "",
                "virtual": 商品状态配置["virtual"],  # 是否为虚拟商品
                "timedPublish": 商品状态配置["timedPublish"],  # 是否定时发布
                "createTime": 商品状态配置["createTime"],  # 创建时间
                "categoryId": 商品分类信息["商品分类ID"],  # 商品分类ID
                "spBizType": 商品分类信息["spBizType"],  # 商品业务类型
                "itemBizType": 商品分类信息["itemBizType"],  # 商品项目业务类型
                "channelCatId": 商品分类信息["channelCatId"],  # 渠道分类ID
                "title": title,  # 使用处理后的标题
                "divisionId": 地区信息["divisionId"],  # 地区ID
                "stuffStatus": 商品状态配置["stuffStatus"],  # 商品状态
                "quantity": self.config["商品基本信息"]["商品数量"],  # 商品数量
                "tradeType": 商品状态配置["tradeType"],  # 交易类型
                "desc": self.config["商品基本信息"]["商品描述模板"].replace("[游戏名字]", game_name),  # 商品描述
                "reservePrice": self.config["商品基本信息"]["商品价格"],  # 商品价格
                "originalPrice": self.config["商品基本信息"]["原价"],  # 原价
                "templateId": 商品状态配置["templateId"],  # 模板ID
                "transportFee": 商品状态配置["transportFee"],  # 运费
                "imgUrls": [img["agisoImgUrl"] for img in uploaded_images],  # 图片URL列表
                "imgList": uploaded_images,  # 完整图片信息列表
                "pvList": [
                    {
                        "propertyId": 商品分类信息["商品分类属性"]["propertyId"],  # 属性ID
                        "propertyName": 商品分类信息["商品分类属性"]["propertyName"],  # 属性名称
                        "channelCatId": 商品分类信息["channelCatId"],  # 渠道分类ID
                        "valueId": 商品分类信息["channelCatId"],  # 值ID
                        "valueName": 商品分类信息["商品分类属性"]["valueName"],  # 值名称
                        "showSubProperty": False  # 是否显示子属性
                    }
                ],
                "divisionIdList": 地区信息["divisionIdList"],  # 地区ID列表
                "afterSalesData": 售后配置["afterSalesData"],  # 售后数据
                "propertyImages": [],  # 属性图片
                "categoryName": 商品分类信息["商品分类名称"],  # 分类名称
                "freeShipping": self.config["其他配置"]["是否包邮"]  # 是否包邮
            }
            
            # 检查配置文件中是否有规格配置，如果有则添加到请求中
            if "规格配置" in self.config:
                self.log_func("检测到多规格配置，添加到发布请求中")
                if "saleProperties" in self.config["规格配置"]:
                    payload["saleProperties"] = self.config["规格配置"]["saleProperties"]
                    self.log_func(f"添加规格属性: {self.config['规格配置']['saleProperties']}")
                if "itemSkuList" in self.config["规格配置"]:
                    # 直接使用配置文件中的SKU列表，不再修改valueText
                    payload["itemSkuList"] = self.config["规格配置"]["itemSkuList"]
                    self.log_func(f"添加规格列表: {len(payload['itemSkuList'])}个SKU")
                
                # 添加goodsType字段
                if "goodsType" in 商品分类信息:
                    payload["goodsType"] = 商品分类信息["goodsType"]
            
            headers = self._build_headers()
            cookies = self._parse_cookie()
            
            # 记录请求数据到日志（不打印到控制台）
            self.log_func(f"准备发布商品: {game_name}")
            
            while retry_count <= max_retries:
                try:
                    retry_msg = f" (重试 {retry_count}/{max_retries})" if retry_count > 0 else ""
                    self.log_func(f"发送商品发布请求{retry_msg}: {game_name}")
                    
                    response = requests.post(
                        "https://aldsidle.agiso.com/api/GoodsManage/Publish",
                        headers=headers,
                        cookies=cookies,
                        data=json.dumps(payload, ensure_ascii=False).encode('utf-8'),
                        timeout=30  # 增加超时时间
                    )
                    
                    # 只在最终发布商品时打印响应信息到控制台
                    try:
                        response_content = response.json()
                        print(f"【{game_name}】发布商品最终响应: {json.dumps(response_content, ensure_ascii=False, indent=2)}")
                    except:
                        print(f"【{game_name}】发布商品最终响应: {response.text}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('succeeded', False):
                            # 检查data中的isSuccess字段，这是真正的发布成功标志
                            data_section = result.get('data', {})
                            is_really_success = data_section.get('isSuccess', False)

                            if is_really_success:
                                # 真正发布成功
                                final_title_bytes = len(title.encode('utf-8'))
                                self.log_func(f"✅ 商品发布成功: {game_name}")
                                self.log_func(f"最终使用的商品标题: {title}")
                                self.log_func(f"最终标题实际字节数: {final_title_bytes}")
                                self.log_func(f"最终标题计算用字节数(+13): {final_title_bytes + 13}")
                                if "规格配置" in self.config:
                                    self.log_func(f"成功发布多规格商品，共{len(self.config['规格配置']['itemSkuList'])}个规格")
                                return True
                            else:
                                # 虽然succeeded为True，但isSuccess为False，说明发布失败
                                error_msg = data_section.get('errorMsg', '未知错误')
                                self.log_func(f"❌ 商品发布失败: {game_name}")
                                self.log_func(f"失败原因: {error_msg}")
                                return False
                        else:
                            self.log_func(f"❌ 商品发布失败: {game_name} - {result.get('message', '未知错误')}")
                            return False
                    elif response.status_code == 503:
                        # 服务器暂时不可用，进行重试
                        retry_count += 1
                        if retry_count <= max_retries:
                            retry_delay = retry_delays[min(retry_count - 1, len(retry_delays) - 1)]
                            self.log_func(f"服务器暂时不可用(503)，将在{retry_delay}秒后进行第{retry_count}次重试")
                            self.log_func(f"商品发布失败(状态码503)，{retry_delay}秒后重试({retry_count}/{max_retries}): {game_name}")
                            time.sleep(retry_delay)
                            continue
                        else:
                            self.log_func(f"服务器暂时不可用(503)，已达到最大重试次数{max_retries}")
                            self.log_func(f"商品发布失败(状态码503)，已达到最大重试次数: {game_name}")
                            return False
                    else:
                        self.log_func(f"发布请求失败，状态码: {response.status_code}")
                        return False
                
                except requests.exceptions.ReadTimeout as e:
                    # 超时错误，尝试重试
                    retry_count += 1
                    if retry_count <= max_retries:
                        retry_delay = retry_delays[min(retry_count - 1, len(retry_delays) - 1)]
                        self.log_func(f"发布超时，将在{retry_delay}秒后进行第{retry_count}次重试")
                        self.log_func(f"商品发布超时，{retry_delay}秒后重试({retry_count}/{max_retries}): {game_name}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.log_func(f"发布超时，已达到最大重试次数{max_retries}")
                        self.log_func(f"商品发布超时，已达到最大重试次数: {game_name}")
                        return False
                        
                except requests.exceptions.ConnectionError as e:
                    # 连接错误，尝试重试
                    retry_count += 1
                    if retry_count <= max_retries:
                        retry_delay = retry_delays[min(retry_count - 1, len(retry_delays) - 1)]
                        self.log_func(f"发布连接错误，将在{retry_delay}秒后进行第{retry_count}次重试")
                        self.log_func(f"商品发布连接错误，{retry_delay}秒后重试({retry_count}/{max_retries}): {game_name}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.log_func(f"发布连接错误，已达到最大重试次数{max_retries}")
                        self.log_func(f"商品发布连接错误，已达到最大重试次数: {game_name}")
                        return False
                    
                except Exception as e:
                    error_msg = f"发布失败: {str(e)}"
                    self.log_func(error_msg)
                    raise PublishError(error_msg)
            
            # 如果所有重试都失败
            return False
            
        except Exception as e:
            error_msg = f"发布失败: {str(e)}"
            self.log_func(error_msg)
            raise PublishError(error_msg)
    
    def process_folder(self, folder_path: str) -> bool:
        """
        处理单个文件夹
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            bool: 是否处理成功
        """
        try:
            self.log_func(f"开始处理文件夹: {folder_path}")
            
            # 获取游戏名称（文件夹名称）
            game_name = os.path.basename(folder_path)
            self.log_func(f"游戏名称: {game_name}")
            
            # 查找图片文件 - 严格按照配置文件中的图片名称列表顺序处理
            image_paths = []
            missing_images = []
            
            # 确保必须有配置文件中的图片名称列表
            if not self.config or "图片配置" not in self.config or "图片名称列表" not in self.config["图片配置"]:
                error_msg = f"{folder_path}: 配置文件中缺少图片名称列表配置"
                self.stats.errors.append(error_msg)
                self.log_func(error_msg)
                self._move_folder(folder_path, "发布失败")
                return False
                
            # 获取配置中的图片名称列表
            image_name_list = self.config["图片配置"]["图片名称列表"]
            self.log_func(f"从配置文件加载图片名称列表: {image_name_list}")
            self.log_func(f"预期上传顺序: {', '.join(image_name_list)}")
            
            # 按照配置中的顺序查找图片
            for image_name in image_name_list:
                image_path = os.path.join(folder_path, image_name)
                if os.path.isfile(image_path):
                    # 验证文件大小，确保不是空文件
                    file_size = os.path.getsize(image_path)
                    if file_size > 0:
                        image_paths.append(image_path)
                        self.log_func(f"找到配置中的图片: {image_name} (大小: {file_size} 字节)")
                    else:
                        self.log_func(f"警告: 图片文件大小为0，跳过: {image_name}")
                        missing_images.append(image_name)
                else:
                    self.log_func(f"警告: 未找到配置中的图片: {image_name}")
                    missing_images.append(image_name)
            
            # 检查是否找到了所有必需的图片
            if len(image_paths) != len(image_name_list):
                error_msg = f"{folder_path}: 未找到所有必需的图片。缺少: {', '.join(missing_images)}"
                self.stats.errors.append(error_msg)
                self.log_func(error_msg)
                self._move_folder(folder_path, "发布失败")
                return False
                
            self.log_func(f"找到所有 {len(image_paths)} 张图片，准备按顺序上传: {[os.path.basename(path) for path in image_paths]}")
            
            # 上传图片 - 一张一张顺序上传，不使用并发
            uploaded_images = []
            
            for i, image_path in enumerate(image_paths):
                image_name = os.path.basename(image_path)
                self.log_func(f"开始上传第 {i+1}/{len(image_paths)} 张图片: {image_name}")
                
                # 单独上传每张图片
                success, result = self.upload_image(image_path)
                
                if success:
                    uploaded_images.append(result)
                    self.log_func(f"✅ 成功上传图片 {i+1}/{len(image_paths)}: {image_name}")
                    # 每次上传成功后等待1秒，避免服务器限流
                    if i < len(image_paths) - 1:
                        time.sleep(1)
                else:
                    self.log_func(f"❌ 上传图片失败 {i+1}/{len(image_paths)}: {image_name} - {result}")
                    error_msg = f"{folder_path}: 图片 {image_name} 上传失败: {result}"
                    self.stats.errors.append(error_msg)
                    self._move_folder(folder_path, "发布失败")
                    return False
            
            # 检查上传结果
            if len(uploaded_images) != len(image_paths):
                error_msg = f"{folder_path}: 图片上传不完整，发布取消"
                self.stats.errors.append(error_msg)
                self.log_func(error_msg)
                self._move_folder(folder_path, "发布失败")
                return False
            
            # 所有图片都已成功上传，继续发布商品
            self.log_func(f"开始发布商品: {game_name}")
            publish_result = self.publish_goods(game_name, uploaded_images)
            
            if publish_result:
                self._move_folder(folder_path, "发布成功")
                with self._lock:
                    self.stats.success_publish += 1
                return True
            else:
                # 发布失败，可能是标题过长或其他原因
                error_msg = f"{folder_path}: 发布失败 - 请查看日志了解详细原因"
                self.stats.errors.append(error_msg)
                self.log_func(f"⚠️ {error_msg}")
                self.log_func("提示: 如果是因为标题长度问题，系统已尝试多级优化，但仍然失败。请考虑手动缩短游戏名称。")
                self._move_folder(folder_path, "发布失败")
                with self._lock:
                    self.stats.failed_publish += 1
                return False
                
        except Exception as e:
            error_msg = f"{folder_path}: {str(e)}"
            self.stats.errors.append(error_msg)
            self.log_func(error_msg)
            self._move_folder(folder_path, "发布失败")
            with self._lock:
                self.stats.failed_publish += 1
            return False
    
    def _move_folder(self, source_path: str, target_dir: str) -> bool:
        """
        移动文件夹
        
        Args:
            source_path: 源文件夹路径
            target_dir: 目标目录
            
        Returns:
            bool: 是否移动成功
        """
        try:
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
            shutil.move(source_path, os.path.join(target_dir, os.path.basename(source_path)))
            self.log_func(f"已移动文件夹到 {target_dir}: {os.path.basename(source_path)}")
            return True
        except Exception as e:
            error_msg = f"移动文件夹失败: {str(e)}"
            self.stats.errors.append(error_msg)
            self.log_func(error_msg)
            return False
    
    def publish_goods_batch(self, folder_paths: List[str], interval: int = 1, progress_callback=None) -> None:
        """
        批量发布商品
        
        Args:
            folder_paths: 文件夹路径列表
            interval: 发布间隔（秒）
            progress_callback: 进度回调函数，参数为(当前索引, 总数量, 成功数, 失败数)
        """
        if not self.config:
            raise ConfigError("未加载配置")
        
        self.is_running = True
        self.stats.reset()
        self.stats.start_time = datetime.now()
        self.stats.total_folders = len(folder_paths)
        self.log_func(f"开始批量发布，共{len(folder_paths)}个文件夹")
        
        try:
            for i, folder in enumerate(folder_paths, 1):
                if not self.is_running:
                    self.log_func("发布任务已手动停止")
                    break
                
                self.log_func(f"处理第 {i}/{len(folder_paths)} 个文件夹")
                success = self.process_folder(folder)
                
                if progress_callback:
                    progress_callback(i, len(folder_paths), self.stats.success_publish, self.stats.failed_publish)
                
                if i < len(folder_paths) and self.is_running:
                    self.log_func(f"等待 {interval} 秒后继续...")
                    time.sleep(interval)
                    
        finally:
            self.stats.end_time = datetime.now()
            self.is_running = False
            self.log_func("批量发布任务完成")
            
            # 输出统计信息
            stats = self.get_stats()
            self.log_func(f"总文件夹数: {stats['总文件夹数']}")
            self.log_func(f"成功发布数: {stats['成功发布数']}")
            self.log_func(f"失败发布数: {stats['失败发布数']}")
            self.log_func(f"总图片数: {stats['总图片数']}")
            self.log_func(f"成功上传图片数: {stats['成功上传图片数']}")
            self.log_func(f"失败上传图片数: {stats['失败上传图片数']}")
            self.log_func(f"运行时间: {stats['运行时间']}")
    
    def stop_publishing(self) -> None:
        """停止发布任务"""
        self.is_running = False
        self.log_func("正在停止发布任务...")
    
    def get_stats(self) -> Dict:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息字典
        """
        if not self.stats.start_time or not self.stats.end_time:
            return {}
            
        time_cost = self.stats.end_time - self.stats.start_time
        hours, remainder = divmod(time_cost.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        return {
            "总文件夹数": self.stats.total_folders,
            "成功发布数": self.stats.success_publish,
            "失败发布数": self.stats.failed_publish,
            "总图片数": self.stats.total_images,
            "成功上传图片数": self.stats.success_upload,
            "失败上传图片数": self.stats.failed_upload,
            "开始时间": self.stats.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "结束时间": self.stats.end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "运行时间": f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}",
            "错误信息": self.stats.errors
        }
        
    def test_auth(self) -> bool:
        """
        测试认证信息是否有效
        
        Returns:
            bool: 认证信息是否有效
        """
        try:
            self.log_func("开始测试认证信息...")
            
            # 构建请求头和Cookie
            headers = self._build_headers()
            cookies = self._parse_cookie()
            
            # 记录认证测试信息到日志（不打印到控制台）
            if self.log_func:
                self.log_func(f"测试认证 - 请求头数量: {len(headers)}")
                self.log_func(f"测试认证 - Cookie数量: {len(cookies)}")
            
            # 发送请求获取用户信息
            response = requests.get(
                "https://aldsidle.agiso.com/api/User/GetUserInfo",
                headers=headers,
                cookies=cookies,
                timeout=10
            )
            
            # 记录响应信息到日志（不打印到控制台）
            if self.log_func:
                self.log_func(f"测试认证 - 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if self.log_func:
                        self.log_func(f"测试认证 - 响应解析成功")
                    if result.get('succeeded', False):
                        self.log_func("认证信息测试成功")
                        return True
                    else:
                        if self.log_func:
                            self.log_func(f"认证信息测试失败: {result.get('message', '未知错误')}")
                        return False
                except Exception as e:
                    if self.log_func:
                        self.log_func(f"认证信息测试失败: 解析响应失败")
                    return False
            else:
                if self.log_func:
                    self.log_func(f"认证信息测试失败: 状态码 {response.status_code}")
                return False
                
        except Exception as e:
            if self.log_func:
                self.log_func(f"认证信息测试失败: {str(e)}")
            return False
